# EF Core CLI commands

## Add a new migration
dotnet ef migrations add MI<PERSON><PERSON>ION_NAME --output-dir "Data/Migrations" --project Bonus.Core --startup-project Bonus.Api --context BonusContext

## Update the database (apply new migrations) (runs automatically on app startup, not needed)
dotnet ef database update --project Bonus.Core --startup-project Bonus.Api --context BonusContext

## Revert last migration (replace previous with the migration before the one you want to revert)
dotnet ef database update <previous> --project Bonus.Core --startup-project Bonus.Api --context BonusContext

## Remove last migration (if applied, first revert)
dotnet ef migrations remove --project Bonus.Core --startup-project Bonus.Api --context BonusContext --force


## Test user
Phone number: 9995551234