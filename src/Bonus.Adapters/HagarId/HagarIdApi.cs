using Bonus.Adapters.HagarId.Models;
using Refit;

namespace Bonus.Adapters.HagarId;

public interface IHagarIdApi
{
    /// <summary>
    /// Starts the authentication process with HagarId
    /// </summary>
    [Post("/api/StartAuthHttp")]
    Task<HagarIdStartAuthResponse> StartAuth([Body] HagarIdStartAuthRequest request, [Query] string code);

    [Post("/api/CheckForAuthResponse")]
    Task<HagarIdCheckAuthResponse> CheckAuthStatus([Body] HagarIdCheckAuthRequest request, [Query] string code);
    
    [Post("/api/AcceptTerms")]
    Task<HagarIdAcceptTermsResponse> AcceptTerms([Body] HagarIdAcceptTermsRequest request, [Query] string code);
}
