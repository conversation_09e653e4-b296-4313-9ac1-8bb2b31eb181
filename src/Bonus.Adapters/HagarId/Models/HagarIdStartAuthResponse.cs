using System.Text.Json.Serialization;

namespace Bonus.Adapters.HagarId.Models;

public class HagarIdStartAuthResponse
{
    [JsonPropertyName("PhoneNumber")]
    public string? PhoneNumber { get; set; }
    
    [JsonPropertyName("Success")]
    public bool Success { get; set; }

    [Json<PERSON>ropertyName("SessionId")]
    public string? SessionId { get; set; }

    [JsonPropertyName("Company")]
    public string? Company { get; set; }

    [JsonPropertyName("Service")]
    public string? Service { get; set; }

    [JsonPropertyName("Message")]
    public string? Message { get; set; }

    [JsonPropertyName("AuthCode")]
    public string? AuthCode { get; set; }

    [JsonPropertyName("Name")]
    public string? Name { get; set; }

    [JsonPropertyName("NationalId")]
    public string? NationalId { get; set; }

    [JsonPropertyName("HagarId")]
    public string? HagarId { get; set; }

    [Json<PERSON>ropertyName("Status")]
    public string? Status { get; set; }

    [JsonPropertyName("Terms")]
    public string? Terms { get; set; }

    [J<PERSON><PERSON>ropertyName("AllowTestUsers")]
    public bool AllowTestUsers { get; set; }

    [JsonPropertyName("IsTestUser")]
    public bool IsTestUser { get; set; }
}
