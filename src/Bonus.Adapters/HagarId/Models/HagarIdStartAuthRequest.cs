using System.Text.Json.Serialization;

namespace Bonus.Adapters.HagarId.Models;

public class HagarIdStartAuthRequest
{
    [JsonPropertyName("PhoneNumber")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? PhoneNumber { get; set; }

    [JsonPropertyName("NationalId")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? NationalId { get; set; }

    [JsonPropertyName("Company")]
    public string Company { get; set; } = null!;

    [JsonPropertyName("Message")]
    public string Message { get; set; } = null!;

    [JsonPropertyName("AllowTestUsers")]
    public bool AllowTestUsers { get; set; } = true;
    
    [JsonPropertyName("GenerateHagarId")]
    public bool GenerateHagarId { get; set; } = true;
}
