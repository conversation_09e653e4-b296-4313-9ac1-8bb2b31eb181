using System.Net.Http.Headers;
using System.Net.Mime;
using Bonus.Shared.Configuration.Settings;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Bonus.Adapters.HagarId;

public class HagarIdHeaderHandler : DelegatingHandler
{
    private readonly ILogger<HagarIdHeaderHandler> _logger;
    private readonly HagarIdApiSettings _hagarIdApiSettings;
    
    public HagarIdHeaderHandler(ILogger<HagarIdHeaderHandler> logger, IOptions<HagarIdApiSettings> hagarIdApiSettings)
    {
        _logger = logger;
        _hagarIdApiSettings = hagarIdApiSettings.Value;
    }
    
    protected override async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
    {
        request.Headers.Accept.Add(new MediaTypeWithQualityHeaderValue(MediaTypeNames.Application.Json));

        var content = await request.Content?.ReadAsStringAsync(cancellationToken)!;
        _logger.LogInformation("Sending request to HagarId API: {Method} {Url} with content: {Content}", request.Method, request.RequestUri, content);
        
        return await base.SendAsync(request, cancellationToken);
    }
}
