using System.Net;
using Bonus.Adapters.LSRetail.Refit;
using Bonus.Adapters.LSRetail.Requests;
using Bonus.Resources;
using Bonus.Shared.Helpers;
using Bonus.Shared.Types.Common;
using Bonus.Shared.Types.Common.Exceptions;
using Microsoft.Extensions.Logging;
using Refit;

namespace Bonus.Adapters.LSRetail;

public class LSRetailAdapter(ILSRetailApi lsRetailApi, ILogger<LSRetailAdapter> logger) : ILSRetailAdapter
{
    public async Task<Result<LSCreateWalletPassResponse, LSRetailError>> CreateWalletPass(LSCreateWalletPassRequest request, CancellationToken cancellationToken) => await CallAsync(lsRetailApi.CreateWalletPass, request, cancellationToken);

    public async Task<Result<LSAcceptTermsResponse, LSRetailError>> AcceptTerms(LSAcceptTermsRequest request, CancellationToken cancellationToken) => await CallAsync(lsRetailApi.AcceptTerms, request, cancellationToken);

    public async Task<Result<LSSocialLogonResponse, LSRetailError>> SocialLogon(LSSocialLogonRequest request, CancellationToken cancellationToken) => await CallAsync(lsRetailApi.SocialLogon, request, cancellationToken);

    public async Task<Result<LSEnvironmentResponse, LSRetailError>> Environment(LSEnvironmentRequest request, CancellationToken cancellationToken) => await CallAsync(lsRetailApi.Environment, request, cancellationToken);

    public async Task<Result<LSGetOneListByCardIdResponse, LSRetailError>> GetOneListByCardId(LSGetOneListByCardIdRequest request, CancellationToken cancellationToken) => await CallAsync(lsRetailApi.GetOneListByCardId, request, cancellationToken);

    public async Task<Result<LSGetOneListByIdResponse, LSRetailError>> GetOneListById(LSGetOneListByIdRequest request, CancellationToken cancellationToken) => await CallAsync(lsRetailApi.GetOneListById, request, cancellationToken);

    public async Task<Result<LSDeleteOneListByIdResponse, LSRetailError>> DeleteOneListById(LSDeleteOneListByIdRequest request, CancellationToken cancellationToken) => await CallAsync(lsRetailApi.DeleteOneListById, request, cancellationToken);

    public async Task<Result<LSLinkOneListResponse, LSRetailError>> LinkOneList(LSLinkOneListRequest request, CancellationToken cancellationToken) => await CallAsync(lsRetailApi.LinkOneList, request, cancellationToken);

    public async Task<Result<LSModifyOneListItemResponse, LSRetailError>> ModifyOneListItem(LSModifyOneListItemRequest request, CancellationToken cancellationToken) => await CallAsync(lsRetailApi.ModifyOneListItem, request, cancellationToken);

    public async Task<Result<LSSaveOneListResponse, LSRetailError>> SaveOneList(LSSaveOneListRequest request, CancellationToken cancellationToken) => await CallAsync(lsRetailApi.SaveOneList, request, cancellationToken);

    public async Task<Result<LSGetHierarchyResponse, LSRetailError>> GetHierarchy(LSGetHierarchyRequest request, CancellationToken cancellationToken) => await CallAsync(lsRetailApi.GetHierarchy, request, cancellationToken);
    
    public async Task<Result<Stream, LSRetailError>> GetImageStreamById(LSGetImageStreamByIdRequest request, CancellationToken cancellationToken) => await CallAsync(lsRetailApi.GetImageStreamById, request, cancellationToken);

    public async Task<Result<LSScanPayGoProfileResponse, LSRetailError>> GetScanPayGoProfile(LSScanPayGoProfileRequest request, CancellationToken cancellationToken) => await CallAsync(lsRetailApi.GetScanPayGoProfile, request, cancellationToken);

    public async Task<Result<LSGetContactGetByCardIdResponse, LSRetailError>> GetContactByCardId(LSGetContactGetByCardIdRequest request, CancellationToken cancellationToken) => await CallAsync(lsRetailApi.GetContactByCardId, request, cancellationToken);

    public async Task<Result<LSContactSearchResponse, LSRetailError>> ContactSearch(LSContactSearchRequest request, CancellationToken cancellationToken) => await CallAsync(lsRetailApi.ContactSearch, request, cancellationToken);

    public async Task<Result<LSContactCreateResponse, LSRetailError>> CreateContact(LSContactCreateRequest request, CancellationToken cancellationToken) => await CallAsync(lsRetailApi.CreateContact, request, cancellationToken);

    public async Task<Result<LSContactUpdateResponse, LSRetailError>> UpdateContact(LSContactUpdateRequest request, CancellationToken cancellationToken) => await CallAsync(lsRetailApi.UpdateContact, request, cancellationToken);

    public async Task<Result<LSContactBlockResponse, LSRetailError>> BlockContact(LSContactBlockRequest request, CancellationToken cancellationToken) => await CallAsync(lsRetailApi.BlockContact, request, cancellationToken);

    public async Task<Result<LSGetAllProfilesResponse, LSRetailError>> GetAllProfiles(LSGetAllProfilesRequest request, CancellationToken cancellationToken) => await CallAsync(lsRetailApi.GetAllProfiles, request, cancellationToken);

    public async Task<Result<LSGetSalesEntriesGetByCardIdResponse, LSRetailError>> GetSalesEntriesByCardId(LSGetSalesEntriesGetByCardIdRequest request, CancellationToken cancellationToken) => await CallAsync(lsRetailApi.GetSalesEntriesByCardId, request, cancellationToken);

    public async Task<Result<LSGetSalesEntryResponse, LSRetailError>> GetSalesEntry(LSGetSalesEntryRequest request, CancellationToken cancellationToken) => await CallAsync(lsRetailApi.GetSalesEntry, request, cancellationToken);
    
    public async Task<Result<LSGetItemsBySearchResponse, LSRetailError>> GetItemsBySearch(LSGetItemsBySearchRequest request, CancellationToken cancellationToken) => await CallAsync(lsRetailApi.GetItemsBySearch, request, cancellationToken);

    public async Task<Result<LSGetItemsByPageResponse, LSRetailError>> GetItemsByPage(LSGetItemsByPageRequest request, CancellationToken cancellationToken) => await CallAsync(lsRetailApi.GetItemsByPage, request, cancellationToken);

    public async Task<Result<LSGetItemByIdResponse, LSRetailError>> GetItemById(LSGetItemGetByIdRequest request, CancellationToken cancellationToken) => await CallAsync(lsRetailApi.GetItemById, request, cancellationToken);

    public async Task<Result<LSGetItemByBarcodeResponse, LSRetailError>> GetItemByBarcode(LSGetItemByBarcodeRequest request, CancellationToken cancellationToken) => await CallAsync(lsRetailApi.GetItemByBarcode, request, cancellationToken);

    public async Task<Result<LSGetStoresResponse, LSRetailError>> GetStores(LSGetStoresRequest request, CancellationToken cancellationToken) => await CallAsync(lsRetailApi.GetStores, request, cancellationToken);

    public async Task<Result<LSGetStoreByIdResponse, LSRetailError>> GetStoreById(LSGetStoreByIdRequest request, CancellationToken cancellationToken) => await CallAsync(lsRetailApi.GetStoreById, request, cancellationToken);

    public async Task<Result<LSGetStoresByCoordinatesResponse, LSRetailError>> GetStoresByCoordinates(LSGetStoresByCoordinatesRequest request, CancellationToken cancellationToken) => await CallAsync(lsRetailApi.GetStoresByCoordinates, request, cancellationToken);

    public async Task<Result<LSReplEcommItemsResponse, LSRetailError>> ReplEcommItems(LSReplEcommItemsRequest request, CancellationToken cancellationToken) => await CallAsync(lsRetailApi.ReplEcommItems, request, cancellationToken);
    
    private async Task<Result<TResponse, LSRetailError>> CallAsync<TRequest, TResponse>(Func<TRequest, CancellationToken, Task<ApiResponse<TResponse>>> apiCall, TRequest request, CancellationToken cancellationToken) where TResponse : class
    {
        var response = await apiCall(request, cancellationToken);

        if (response.StatusCode is HttpStatusCode.InternalServerError)
        {
            throw new BonusException(nameof(Translations.Communication_UnexpectedError));
        }
            
        if (response.IsSuccessStatusCode && response.Error is null)
        {
            return Result<TResponse, LSRetailError>.Ok(response.Content!);
        }

        var rawContent = response.Error.Content;
        
        logger.LogWarning("Received error response from LSRetail API: {StatusCode} - {Content}",
            response.StatusCode, rawContent ?? "No content");

        var cleanContent = rawContent?.Replace("\\\"", "\"").Trim('\"');

        var errorResponse = cleanContent?.Deserialize<LSRetailError>();

        if (errorResponse is null)
        {
            throw new BonusException(nameof(Translations.Communication_UnexpectedError));
        }
        
        return Result<TResponse, LSRetailError>.BadRequest(errorResponse, rawContent!);
    }

}

public interface ILSRetailAdapter
{
    Task<Result<LSCreateWalletPassResponse, LSRetailError>> CreateWalletPass(LSCreateWalletPassRequest request, CancellationToken cancellationToken);
    
    Task<Result<LSAcceptTermsResponse, LSRetailError>> AcceptTerms(LSAcceptTermsRequest request, CancellationToken cancellationToken);
    
    Task<Result<LSSocialLogonResponse, LSRetailError>> SocialLogon(LSSocialLogonRequest request, CancellationToken cancellationToken);
    
    Task<Result<LSEnvironmentResponse, LSRetailError>> Environment(LSEnvironmentRequest request, CancellationToken cancellationToken);
    
    Task<Result<LSGetOneListByCardIdResponse, LSRetailError>> GetOneListByCardId(LSGetOneListByCardIdRequest request, CancellationToken cancellationToken);
    
    Task<Result<LSGetOneListByIdResponse, LSRetailError>> GetOneListById(LSGetOneListByIdRequest request, CancellationToken cancellationToken);
    
    Task<Result<LSDeleteOneListByIdResponse, LSRetailError>> DeleteOneListById(LSDeleteOneListByIdRequest request, CancellationToken cancellationToken);
    
    Task<Result<LSLinkOneListResponse, LSRetailError>> LinkOneList(LSLinkOneListRequest request, CancellationToken cancellationToken);
    
    Task<Result<LSModifyOneListItemResponse, LSRetailError>> ModifyOneListItem(LSModifyOneListItemRequest request, CancellationToken cancellationToken);
    
    Task<Result<LSSaveOneListResponse, LSRetailError>> SaveOneList(LSSaveOneListRequest request, CancellationToken cancellationToken);
    
    Task<Result<LSGetHierarchyResponse, LSRetailError>> GetHierarchy(LSGetHierarchyRequest request, CancellationToken cancellationToken);
    
    Task<Result<Stream, LSRetailError>> GetImageStreamById(LSGetImageStreamByIdRequest request, CancellationToken cancellationToken);
    
    Task<Result<LSScanPayGoProfileResponse, LSRetailError>> GetScanPayGoProfile(LSScanPayGoProfileRequest request, CancellationToken cancellationToken);
    
    Task<Result<LSGetContactGetByCardIdResponse, LSRetailError>> GetContactByCardId(LSGetContactGetByCardIdRequest request, CancellationToken cancellationToken);
    
    Task<Result<LSContactSearchResponse, LSRetailError>> ContactSearch(LSContactSearchRequest request, CancellationToken cancellationToken);
    
    Task<Result<LSContactCreateResponse, LSRetailError>> CreateContact(LSContactCreateRequest request, CancellationToken cancellationToken);
    
    Task<Result<LSContactUpdateResponse, LSRetailError>> UpdateContact(LSContactUpdateRequest request, CancellationToken cancellationToken);
    
    Task<Result<LSContactBlockResponse, LSRetailError>> BlockContact(LSContactBlockRequest request, CancellationToken cancellationToken);
    
    Task<Result<LSGetAllProfilesResponse, LSRetailError>> GetAllProfiles(LSGetAllProfilesRequest request, CancellationToken cancellationToken);
    
    Task<Result<LSGetSalesEntriesGetByCardIdResponse, LSRetailError>> GetSalesEntriesByCardId(LSGetSalesEntriesGetByCardIdRequest request, CancellationToken cancellationToken);
    
    Task<Result<LSGetSalesEntryResponse, LSRetailError>> GetSalesEntry(LSGetSalesEntryRequest request, CancellationToken cancellationToken);
    
    Task<Result<LSGetItemsBySearchResponse, LSRetailError>> GetItemsBySearch(LSGetItemsBySearchRequest request, CancellationToken cancellationToken);
    
    Task<Result<LSGetItemsByPageResponse, LSRetailError>> GetItemsByPage(LSGetItemsByPageRequest request, CancellationToken cancellationToken);
    
    Task<Result<LSGetItemByIdResponse, LSRetailError>> GetItemById(LSGetItemGetByIdRequest request, CancellationToken cancellationToken);
    
    Task<Result<LSGetItemByBarcodeResponse, LSRetailError>> GetItemByBarcode(LSGetItemByBarcodeRequest request, CancellationToken cancellationToken);
    
    Task<Result<LSGetStoresResponse, LSRetailError>> GetStores(LSGetStoresRequest request, CancellationToken cancellationToken);
    
    Task<Result<LSGetStoreByIdResponse, LSRetailError>> GetStoreById(LSGetStoreByIdRequest request, CancellationToken cancellationToken);
    
    Task<Result<LSGetStoresByCoordinatesResponse, LSRetailError>> GetStoresByCoordinates(LSGetStoresByCoordinatesRequest request, CancellationToken cancellationToken);

    Task<Result<LSReplEcommItemsResponse, LSRetailError>> ReplEcommItems(LSReplEcommItemsRequest request, CancellationToken cancellationToken);
}