using System.Text.Json.Serialization;
using Bonus.Adapters.LSRetail.Models.Members;

namespace Bonus.Adapters.LSRetail.Requests;

public class LSContactUpdateRequest
{
    [JsonPropertyName("contact")]
    public required MemberContact Contact { get; set; }

    [JsonPropertyName("getContact")]
    public required bool GetContact { get; set; } = true;
}

public class LSContactUpdateResponse
{
    [JsonPropertyName("ContactUpdateResult")]
    public MemberContact? Contact { get; set; }
}