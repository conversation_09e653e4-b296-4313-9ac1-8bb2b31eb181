using System.Text.Json.Serialization;
using Bonus.Adapters.LSRetail.Models.Hierarchies;

namespace Bonus.Adapters.LSRetail.Requests;

public class LSGetHierarchyRequest
{
    [JsonPropertyName("storeId")]
    public required string StoreId { get; set; } = "01";
}

public class LSGetHierarchyResponse
{
    [JsonPropertyName("HierarchyGetResult")]
    public List<Hierarchy> Hierarchy { get; set; } = [];
}