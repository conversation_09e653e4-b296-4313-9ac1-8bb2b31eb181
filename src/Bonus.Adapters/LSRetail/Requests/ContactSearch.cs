using System.Text.Json.Serialization;
using Bonus.Adapters.LSRetail.Enums;
using Bonus.Adapters.LSRetail.Models.Members;

namespace Bonus.Adapters.LSRetail.Requests;

public class LSContactSearchRequest
{
    [JsonPropertyName("searchType")]
    public required ContactSearchType SearchType { get; set; } = ContactSearchType.Name;

    [JsonPropertyName("search")]
    public required string Search { get; set; }

    [JsonPropertyName("maxNumberOfRowsReturned")]
    public required int MaxNumberOfRowsReturned { get; set; } = 1;
}

public class LSContactSearchResponse
{
    [Json<PERSON>ropertyName("ContactSearchResult")]
    public required List<MemberContact> Contacts { get; set; }
}