using System.Text.Json.Serialization;
using Bonus.Adapters.LSRetail.Models.Baskets;

namespace Bonus.Adapters.LSRetail.Requests;

public class LSModifyOneListItemRequest
{
    [JsonPropertyName("oneListId")]
    public required string OneListId { get; set; }
	
    [JsonPropertyName("item")]
	public required OneListItem Item { get; set; }
	
	[JsonPropertyName("cardId")]
	public required string CardId { get; set; }
	
	[JsonPropertyName("remove")]
	public bool Remove { get; set; }
	
	[JsonPropertyName("calculate")]
	public bool Calculate { get; set; }
}

public class LSModifyOneListItemResponse
{
	[JsonPropertyName("OneListItemModifyResult")]
    public OneList OneList { get; set; }
}