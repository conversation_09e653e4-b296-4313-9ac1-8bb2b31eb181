using System.Text.Json.Serialization;
using Bonus.Adapters.LSRetail.Enums;

namespace Bonus.Adapters.LSRetail.Requests;

public class LSLinkOneListRequest
{
    [JsonPropertyName("oneListId")]
    public required string OneListId { get; set; }
    
    [JsonPropertyName("cardId")]
    public string? CardId { get; set; }
    
    [JsonPropertyName("email")]
    public string? Email { get; set; }
    
    [JsonPropertyName("phone")]
    public string? Phone { get; set; }
    
    [JsonPropertyName("status")]
    public LinkStatus Status { get; set; } = LinkStatus.Requesting;
}

// Returns 416 Requested Range Not Satisfiable if card/email/phone is not valid or user does not exist.
// Or if the OneListId is not valid or doesn't exist.
public class LSLinkOneListResponse
{
    [JsonPropertyName("OneListLinkingResult")]
    public required bool Success { get; set; }
}