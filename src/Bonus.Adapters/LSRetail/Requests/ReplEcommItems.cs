using System.Text.Json.Serialization;

namespace Bonus.Adapters.LSRetail.Requests;

public class LSReplEcommItemsRequest
{
    [JsonPropertyName("replRequest")]
    public required LSReplRequest ReplRequest { get; set; }
}

public class LSReplRequest
{
    public string AppId { get; set; } = string.Empty;

    public int BatchSize { get; set; } = 1000;

    public bool FullReplication { get; set; } = true;

    public string LastKey { get; set; } = string.Empty;

    public string MaxKey { get; set; } = string.Empty;

    public string StoreId { get; set; } = string.Empty;

    public string TerminalId { get; set; } = string.Empty;
}

public class LSReplEcommItemsResponse
{
    [JsonPropertyName("ReplEcommItemsResult")]
    public required LSReplEcommItemsResult Result { get; set; }
}

public class LSReplEcommItemsResult
{
    public List<LSReplEcommItem> Items { get; set; } = [];

    public string LastKey { get; set; } = string.Empty;

    public string MaxKey { get; set; } = string.Empty;

    public int RecordsRemaining { get; set; }
}

public class LSReplEcommItem
{
    public required string Id { get; set; }

    public string? Description { get; set; }

    public string? Details { get; set; }

    public decimal UnitPrice { get; set; }

    public string? ItemCategoryCode { get; set; }

    [JsonPropertyName("SalseUnitOfMeasure")]
    public string? SalesUnitOfMeasure { get; set; }

    public bool IsDeleted { get; set; }
}
