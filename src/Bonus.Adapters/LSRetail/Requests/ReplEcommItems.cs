using System.Text.Json.Serialization;

namespace Bonus.Adapters.LSRetail.Requests;

public class LSReplEcommItemsRequest
{
    [JsonPropertyName("replRequest")]
    public required LSReplRequest ReplRequest { get; set; }
}

public class LSReplRequest
{
    [JsonPropertyName("appId")]
    public string AppId { get; set; } = string.Empty;
    
    [JsonPropertyName("appType")]
    public string AppType { get; set; } = string.Empty;
    
    [JsonPropertyName("storeId")]
    public string StoreId { get; set; } = string.Empty;
    
    [JsonPropertyName("terminalId")]
    public string TerminalId { get; set; } = string.Empty;
    
    [JsonPropertyName("batchSize")]
    public int BatchSize { get; set; } = 1000;
    
    [JsonPropertyName("fullReplication")]
    public bool FullReplication { get; set; } = true;
    
    [JsonPropertyName("lastKey")]
    public string LastKey { get; set; } = string.Empty;
    
    [JsonPropertyName("maxKey")]
    public string MaxKey { get; set; } = string.Empty;
}

public class LSReplEcommItemsResponse
{
    [JsonPropertyName("ReplEcommItemsResult")]
    public required LSReplEcommItemsResult Result { get; set; }
}

public class LSReplEcommItemsResult
{
    [JsonPropertyName("TableData")]
    public List<LSReplEcommItem> Items { get; set; } = [];
    
    [JsonPropertyName("LastKey")]
    public string LastKey { get; set; } = string.Empty;
    
    [JsonPropertyName("MaxKey")]
    public string MaxKey { get; set; } = string.Empty;
    
    [JsonPropertyName("RecordsRemaining")]
    public int RecordsRemaining { get; set; }
}

public class LSReplEcommItem
{
    [JsonPropertyName("Id")]
    public required string Id { get; set; }
    
    [JsonPropertyName("Description")]
    public string? Description { get; set; }
    
    [JsonPropertyName("Details")]
    public string? Details { get; set; }
    
    [JsonPropertyName("BaseUnitOfMeasure")]
    public string? BaseUnitOfMeasure { get; set; }
    
    [JsonPropertyName("SalseUnitOfMeasure")]
    public string? SalesUnitOfMeasure { get; set; }
    
    [JsonPropertyName("PurchUnitOfMeasure")]
    public string? PurchaseUnitOfMeasure { get; set; }
    
    [JsonPropertyName("UnitPrice")]
    public decimal UnitPrice { get; set; }
    
    [JsonPropertyName("GrossWeight")]
    public decimal GrossWeight { get; set; }
    
    [JsonPropertyName("UnitVolume")]
    public decimal UnitVolume { get; set; }
    
    [JsonPropertyName("UnitsPerParcel")]
    public decimal UnitsPerParcel { get; set; }
    
    [JsonPropertyName("ItemCategoryCode")]
    public string? ItemCategoryCode { get; set; }
    
    [JsonPropertyName("ItemFamilyCode")]
    public string? ItemFamilyCode { get; set; }
    
    [JsonPropertyName("ItemTrackingCode")]
    public string? ItemTrackingCode { get; set; }
    
    [JsonPropertyName("ProductGroupId")]
    public string? ProductGroupId { get; set; }
    
    [JsonPropertyName("SeasonCode")]
    public string? SeasonCode { get; set; }
    
    [JsonPropertyName("TariffNo")]
    public string? TariffNo { get; set; }
    
    [JsonPropertyName("TaxItemGroupId")]
    public string? TaxItemGroupId { get; set; }
    
    [JsonPropertyName("VendorId")]
    public string? VendorId { get; set; }
    
    [JsonPropertyName("VendorItemId")]
    public string? VendorItemId { get; set; }
    
    [JsonPropertyName("CountryOfOrigin")]
    public string? CountryOfOrigin { get; set; }
    
    [JsonPropertyName("Type")]
    public int Type { get; set; }
    
    [JsonPropertyName("Blocked")]
    public int Blocked { get; set; }
    
    [JsonPropertyName("BlockedOnPos")]
    public int BlockedOnPos { get; set; }
    
    [JsonPropertyName("BlockedOnECom")]
    public int BlockedOnECom { get; set; }
    
    [JsonPropertyName("BlockDiscount")]
    public int BlockDiscount { get; set; }
    
    [JsonPropertyName("BlockDistribution")]
    public int BlockDistribution { get; set; }
    
    [JsonPropertyName("BlockManualPriceChange")]
    public int BlockManualPriceChange { get; set; }
    
    [JsonPropertyName("BlockNegativeAdjustment")]
    public int BlockNegativeAdjustment { get; set; }
    
    [JsonPropertyName("BlockPositiveAdjustment")]
    public int BlockPositiveAdjustment { get; set; }
    
    [JsonPropertyName("BlockPurchaseReturn")]
    public int BlockPurchaseReturn { get; set; }
    
    [JsonPropertyName("KeyingInPrice")]
    public int KeyingInPrice { get; set; }
    
    [JsonPropertyName("KeyingInQty")]
    public int KeyingInQty { get; set; }
    
    [JsonPropertyName("MustKeyInComment")]
    public int MustKeyInComment { get; set; }
    
    [JsonPropertyName("NoDiscountAllowed")]
    public int NoDiscountAllowed { get; set; }
    
    [JsonPropertyName("ScaleItem")]
    public int ScaleItem { get; set; }
    
    [JsonPropertyName("ZeroPriceValId")]
    public int ZeroPriceValidation { get; set; }
    
    [JsonPropertyName("CrossSellingExists")]
    public int CrossSellingExists { get; set; }
    
    [JsonPropertyName("IsDeleted")]
    public bool IsDeleted { get; set; }
}
