using System.Text.Json.Serialization;
using Bonus.Adapters.LSRetail.Models.Items;

namespace Bonus.Adapters.LSRetail.Requests;

public class LSGetItemsByPageResponse
{
    [JsonPropertyName("ItemsPageResult")]
    public List<LoyaltyItem> Items { get; set; } = [];
}

public class LSGetItemsByPageRequest
{
    [JsonPropertyName("storeId")]
    public required string StoreId { get; set; }

    [JsonPropertyName("pageSize")]
    public int PageSize { get; set; }

    [JsonPropertyName("pageNumber")]
    public int PageNumber { get; set; }

    [JsonPropertyName("itemCategoryId")]
    public string? ItemCategoryId { get; set; }

    [JsonPropertyName("productGroupId")]
    public string? ProductGroupId { get; set; }

    [JsonPropertyName("search")]
    public string? Search { get; set; }

    [JsonPropertyName("includeDetails")]
    public bool IncludeDetails { get; set; } = false;
}