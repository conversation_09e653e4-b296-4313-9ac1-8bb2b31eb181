using System.Text.Json.Serialization;
using Bonus.Adapters.LSRetail.Enums;

namespace Bonus.Adapters.LSRetail.Requests;

public class LSCreateWalletPassRequest
{
    [JsonPropertyName("cardId")]
    public required string CardId { get; set; }

    [JsonPropertyName("platformType")]
    public required PlatformType PlatformType { get; set; }
}

public class LSCreateWalletPassResponse
{
    [JsonPropertyName("CreateWalletPassResult")]
    public required string Url { get; set; }
}