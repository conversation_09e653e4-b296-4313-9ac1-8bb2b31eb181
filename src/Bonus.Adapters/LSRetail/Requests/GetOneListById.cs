using System.Text.Json.Serialization;
using Bonus.Adapters.LSRetail.Models.Baskets;

namespace Bonus.Adapters.LSRetail.Requests;

public class LSGetOneListByIdRequest
{
    [JsonPropertyName("id")]
    public required string Id { get; set; }
    
    [JsonPropertyName("includeLines")]
    public bool IncludeLines { get; set; } = true;
}

public class LSGetOneListByIdResponse
{
    [JsonPropertyName("OneListGetByIdResult")]
    public required OneList OneList { get; set; }
}