using System.Text.Json.Serialization;
using Bonus.Adapters.LSRetail.Models.Members;

namespace Bonus.Adapters.LSRetail.Requests;

public class LSSocialLogonRequest
{
    [JsonPropertyName("authenticator")]
    public required string Authenticator { get; set; }
    
    [JsonPropertyName("authenticationId")]
    public required string AuthenticationId { get; set; }
    
    [JsonPropertyName("deviceId")]
    public required string DeviceId { get; set; }
    
    [JsonPropertyName("deviceName")]
    public string? DeviceName { get; set; }

    [JsonPropertyName("includeDetails")]
    public bool IncludeDetails { get; set; } = false;
}

public class LSSocialLogonResponse
{
    [JsonPropertyName("SocialLogonResult")]
    public required MemberContact? Contact { get; set; }
}