using System.Text.Json.Serialization;

namespace Bonus.Adapters.LSRetail.Requests;

public class LSAcceptTermsRequest
{
    [JsonPropertyName("accountId")]
    public required string AccountId { get; set; }
    
    [JsonPropertyName("deviceId")]
    public required string DeviceId { get; set; }
    
    [JsonPropertyName("termsAndConditionsVersion")]
    public required string TermsAndConditionsVersion { get; set; }
    
    [JsonPropertyName("privacyPolicyVersion")]
    public required string PrivacyPolicyVersion { get; set; }
}

// Returns 200 OK on the first go for a specific account Id, and then 404 Not Found for already confirmed. (unless terms version differs)
public class LSAcceptTermsResponse
{
    [JsonPropertyName("AcceptTermsResult")]
    public required bool Success { get; set; }
}