using System.Text.Json.Serialization;

namespace Bonus.Adapters.LSRetail.Requests;

public class LSContactBlockRequest
{
    [JsonPropertyName("cardId")]
    public required string CardId { get; set; }
    
    [JsonPropertyName("accountId")]
    public required string AccountId { get; set; }
}

public class LSContactBlockResponse
{
    [JsonPropertyName("ContactBlockResult")]
    public required bool Success { get; set; }
}