using System.Text.Json.Serialization;
using Bonus.Adapters.LSRetail.Models.Items;

namespace Bonus.Adapters.LSRetail.Requests;

public class LSGetItemsBySearchResponse
{
    [JsonPropertyName("ItemsSearchResult")]
    public List<LoyaltyItem> Items { get; set; } = [];
}

public class LSGetItemsBySearchRequest
{
    [JsonPropertyName("search")]
    public required string Search { get; set; }

    [JsonPropertyName("maxNumberOfItems")]
    public int MaxNumberOfItems { get; set; } = Int32.MaxValue;
    
    [JsonPropertyName("includeDetails")]
    public bool IncludeDetails { get; set; } = false;
}