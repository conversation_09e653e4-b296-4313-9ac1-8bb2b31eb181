using System.Text.Json.Serialization;
using Bonus.Adapters.LSRetail.Enums;
using Bonus.Adapters.LSRetail.Models.Baskets;

namespace Bonus.Adapters.LSRetail.Requests;

public class LSGetOneListByCardIdRequest
{
    [JsonPropertyName("cardId")]
    public required string CardId { get; set; }
    
    [JsonPropertyName("listType")]
    public required ListType ListType { get; set; }
    
    [JsonPropertyName("includeLines")]
    public bool IncludeLines { get; set; } = false;
}

public class LSGetOneListByCardIdResponse
{
    [JsonPropertyName("OneListGetByCardIdResult")]
    public List<OneList> OneLists { get; set; } = [];
}