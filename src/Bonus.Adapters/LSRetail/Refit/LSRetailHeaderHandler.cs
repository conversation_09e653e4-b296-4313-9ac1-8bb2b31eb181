using System.Net.Http.Headers;
using System.Net.Mime;
using Microsoft.Extensions.Logging;

namespace Bonus.Adapters.LSRetail.Refit;

public class LSRetailHeaderHandler(ILogger<LSRetailHeaderHandler> logger) : DelegatingHandler
{
    protected override async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
    {
        request.Headers.Add("LSRETAIL-TOKEN", "SecurityToken");
        request.Headers.Add("LSRETAIL-DEVICEID", ""); 
        request.Headers.Add("LSRETAIL-KEY", "");
        request.Headers.Add("User-Agent", "AppVer-2024.10.0.0");
        
        request.Headers.Accept.Add(new MediaTypeWithQualityHeaderValue(MediaTypeNames.Application.Json));
        
        var content = request.Content is not null ? await request.Content.ReadAsStringAsync(cancellationToken) : string.Empty;
        logger.LogInformation("Sending request to LSRetail API: {Method} {Url} with content: {Content}", request.Method, request.RequestUri, content);

        return await base.SendAsync(request, cancellationToken);
    }
}