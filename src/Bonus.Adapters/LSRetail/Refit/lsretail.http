@baseUrl = http://bonuabcgg-ap01/CommerceServiceNew/ucjson.svc

### Contact Create
POST {{baseUrl}}/ContactCreate
Accept: application/json
Content-Type: application/json

{
    "contact": {
        "UserName": "test5",
        "Password": "test123456789",
        "Email": "<EMAIL>",
        "authenticator": "HagarAuthenticator",
        "authenticationId": "HAGAR-19562c44-f9f5-4d70-ac5e-9ff065b76750",
        "SendReceiptByEMail": 1,
        "Initials": "",
        "FirstName": "Test",
        "MiddleName": "",
        "LastName": "Test",
        "Addresses": [
            {
                "Id": "",
                "Type": 0,
                "Address1": "",
                "Address2": "",
                "HouseNo": "",
                "City": "",
                "PostCode": "",
                "StateProvinceRegion": "",
                "Country": "",
                "County": "",
                "CellPhoneNumber": "",
                "PhoneNumber": "8487086"
            }
        ],
        "Gender": 0,
        "MaritalStatus": 0,
        "BirthDay": "/Date(-*************)/",
        "LoggedOnToDevice": null,
        "Blocked": false,
        "BlockedBy": null,
        "BlockedReason": null,
        "GuestType": null,
        "Name": "Test test",
        "Notifications": [],
        "PublishedOffers": [],
        "Profiles": [
            {
                "Description": "Kennitala aðildarfélaga",
                "DataType": 0,
                "DefaultValue": "",
                "Mandatory": true,
                "ContactValue": false,
                "TextValue": "3333333339",
                "Id": "KENNITALA"
            }
        ],
        "SalesEntries": [],
        "OneLists": [],
        "Environment": {
            "Currency": {
                "Prefix": "",
                "Postfix": "",
                "Symbol": "",
                "RoundOffSales": 0.01,
                "RoundOffAmount": 0.01,
                "DecimalSeparator": "",
                "ThousandSeparator": "",
                "Description": "",
                "DecimalPlaces": 2,
                "SaleRoundingMethod": 0,
                "AmountRoundingMethod": 0,
                "Culture": "",
                "Id": ""
            },
            "PasswordPolicy": "",
            "Version": ""
        },
        "AlternateId": null,
        "ExternalSystem": null,
        "Cards": [],
        "Account": null,
        "Id": ""
    },
    "doLogin": true
}

### Contact Update
POST {{baseUrl}}/ContactUpdate
Accept: application/json
Content-Type: application/json
User-Agent: AppVer-2024.10.0.0

{
  "contact": {
    "UserName": "",
    "Password": "",
    "Email": "<EMAIL>",
    "authenticator": "HagarAuthenticator",
    "authenticationId": "HAGAR-19562c44-f9f5-4d70-ac5e-9ff065b76750",
    "SendReceiptByEMail": 1,
    "Initials": "",
    "FirstName": "Test",
    "MiddleName": "",
    "LastName": "Test",
    "Addresses": [
      {
        "Id": "",
        "Type": 0,
        "Address1": "",
        "Address2": "",
        "HouseNo": "",
        "City": "",
        "PostCode": "",
        "StateProvinceRegion": "",
        "Country": "",
        "County": "",
        "CellPhoneNumber": "",
        "PhoneNumber": "8487086"
      }
    ],
    "Gender": 0,
    "MaritalStatus": 0,
    "BirthDay": "/Date(-*************)/",
    "LoggedOnToDevice": null,
    "Blocked": false,
    "BlockedBy": null,
    "BlockedReason": null,
    "GuestType": null,
    "Name": "Test test",
    "Notifications": [],
    "PublishedOffers": [],
    "Profiles": [
      {
        "Description": "Kennitala aðildarfélaga",
        "DataType": 0,
        "DefaultValue": "",
        "Mandatory": true,
        "ContactValue": false,
        "TextValue": "3333333339",
        "Id": "KENNITALA"
      }
    ],
    "SalesEntries": [],
    "OneLists": [],
    "Environment": {
      "Currency": {
        "Prefix": "",
        "Postfix": "",
        "Symbol": "",
        "RoundOffSales": 0.01,
        "RoundOffAmount": 0.01,
        "DecimalSeparator": "",
        "ThousandSeparator": "",
        "Description": "",
        "DecimalPlaces": 2,
        "SaleRoundingMethod": 0,
        "AmountRoundingMethod": 0,
        "Culture": "",
        "Id": ""
      },
      "PasswordPolicy": "",
      "Version": ""
    },
    "AlternateId": null,
    "ExternalSystem": null,
    "Cards": [],
    "Account": null,
    "Id": ""
  }
}

### Contact Search
POST {{baseUrl}}/ContactSearch
Accept: application/json
Content-Type: application/json

{
    "search": "Ad",
    "searchType": 4,
    "pageSize": 100,
    "pageNumber": 1
}

### Get Contact By Card ID
POST {{baseUrl}}/ContactGetByCardId
Accept: application/json
Content-Type: application/json

{
    "cardId": "BON20004170525311024"
}

### Environment
POST {{baseUrl}}/Environment
Accept: application/json
Content-Type: application/json

{}

### Social Logon
POST {{baseUrl}}/SocialLogon
Accept: application/json
Content-Type: application/json

{
    "authenticator": "HagarAuthenticator",
    "authenticationId": "HAGAR-19562c44-f9f5-4d70-ac5e-9ff065b76750",
    "deviceId": "WEB-test5",
    "deviceName": "Web application",
    "includeDetails": true
}

### Get One List By Card ID
POST {{baseUrl}}/OneListGetByCardId
Accept: application/json
Content-Type: application/json

{
    "cardId": "BON11415343849110725",
    "itemsPerList": 100
}

### Get One List By ID
POST {{baseUrl}}/OneListGetById
Accept: application/json
Content-Type: application/json

{
    "oneListId": ""
}

### Link One List
POST {{baseUrl}}/OneListLinking
Accept: application/json
Content-Type: application/json

{
    "oneList": {},
    "linkStatus": 0
}

### Save One List
POST {{baseUrl}}/OneListSave
Accept: application/json
Content-Type: application/json

{
    "oneList": {}
}

### Get Hierarchy
POST {{baseUrl}}/HierarchyGet
Accept: application/json
Content-Type: application/json

{
  "storeId": "01"
}

### Get Image Stream By ID
# Note: This is a GET request
GET {{baseUrl}}/ImageStreamGetById?id=your-image-id
Accept: application/json

### Get Scan Pay Go Profile
POST {{baseUrl}}/ScanPayGoProfileGet
Accept: application/json
Content-Type: application/json

{}

### Gets all Member Attributes that are available to assign to a Member Contact
POST {{baseUrl}}/ProfilesGetAll
Accept: application/json
Content-Type: application/json

{}

### Get Sales Entries By Card ID
POST {{baseUrl}}/SalesEntriesGetByCardId
Accept: application/json
Content-Type: application/json

{
    "cardId": "",
    "pageSize": 100,
    "pageNumber": 1
}

### Items Search
POST {{baseUrl}}/ItemsSearch
Accept: application/json
Content-Type: application/json

{
    "search": "a",
    "pageSize": 100,
    "pageNumber": 1,
  "includeDetails": true
}

### Items page
POST {{baseUrl}}/ItemsPage
Accept: application/json
Content-Type: application/json

{
  "storeId": "01",
  "pageSize": 1000,
  "pageNumber": 6,
  "includeDetails": true
}

### Items page
POST {{baseUrl}}/ItemCategoriesGetById
Accept: application/json
Content-Type: application/json

{
  "itemCategoryId": "10023"
}

### Get Item By ID

POST {{baseUrl}}/ItemGetById
Accept: application/json
Content-Type: application/json

{
    "itemId": "16727"
}

### Get Item By ID
POST {{baseUrl}}/ItemCategoriesGetAll
Accept: application/json
Content-Type: application/json

{
}

### Get Item By Barcode
POST {{baseUrl}}/ItemGetByBarcode
Accept: application/json
Content-Type: application/json

{
    "barcode": ""
}

### Get Stores
POST {{baseUrl}}/StoresGet
Accept: application/json
Content-Type: application/json

{}

### Items in store
POST {{baseUrl}}/ItemCustomerPricesGet
Accept: application/json
Content-Type: application/json

{
  "storeId": "01",
  "cardId": "BON11263037967240725"
}

### Items in store
POST {{baseUrl}}/Search
Accept: application/json
Content-Type: application/json

{
  "search": "mjolk",
  "SearchType": "1"
}

### Get Store By ID
POST {{baseUrl}}/StoreGetById
Accept: application/json
Content-Type: application/json

{
    "storeId": "18"
}

### Get Stores By Coordinates
POST {{baseUrl}}/StoresGetByCoordinates
Accept: application/json
Content-Type: application/json

{
    "latitude": 64.12806048699696,
    "longitude": -21.89942388063276,
    "maxDistance": 50
}

### Get repl ecomm items
POST {{baseUrl}}/ReplEcommItems
Accept: application/json
Content-Type: application/json

{
  "replRequest":
  {
      "AppId": "1",
      "BatchSize": 10,
      "FullReplication": true,
      "LastKey": 0,
      "MaxKey": 0,
      "StoreId": "01",
      "TerminalId": ""
    }
}

### Get repl ecomm items
POST {{baseUrl}}/ReplEcommFullItem
Accept: application/json
Content-Type: application/json

{
  "replRequest":
  {
    "AppId": "1",
    "BatchSize": 10,
    "FullReplication": true,
    "LastKey": 0,
    "MaxKey": 0,
    "StoreId": "01",
    "TerminalId": ""
  }
}

### Get repl ecomm hierarchy
POST {{baseUrl}}/ReplEcommHierarchyNode
Accept: application/json
Content-Type: application/json

{
  "replRequest":
  {
    "AppId": "1",
    "BatchSize": 10000,
    "FullReplication": true,
    "LastKey": 0,
    "MaxKey": 0,
    "StoreId": "01",
    "TerminalId": ""
  }
}
