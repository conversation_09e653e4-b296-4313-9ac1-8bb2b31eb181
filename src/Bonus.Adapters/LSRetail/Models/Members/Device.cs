using System.Text.Json.Serialization;

namespace Bonus.Adapters.LSRetail.Models.Members;

public class Device
{
    [JsonPropertyName("Id")]
    public string Id { get; set; }
    
    [JsonPropertyName("DeviceFriendlyName")]
    public string? DeviceFriendlyName { get; set; }
    
    [Json<PERSON>ropertyName("CardId")]
    public string? CardId { get; set; }
    
    [Json<PERSON>ropertyName("Manufacturer")]
    public string? Manufacturer { get; set; }
    
    [JsonPropertyName("Model")]
    public string? Model { get; set; }
    
    [JsonPropertyName("OsVersion")]
    public string? OsVersion { get; set; }
    
    [JsonPropertyName("Platform")]
    public string? Platform { get; set; }
    
    [JsonPropertyName("SecurityToken")]
    public string? SecurityToken { get; set; }
}