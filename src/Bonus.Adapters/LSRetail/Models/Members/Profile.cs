using System.Text.Json.Serialization;

namespace Bonus.Adapters.LSRetail.Models.Members;

public class Profile
{
    [JsonPropertyName("Id")]
    public string? Id { get; set; }
    
    [JsonPropertyName("Description")]
    public string? Description { get; set; }
    
    [JsonPropertyName("DataType")]
    public int DataType { get; set; }
    
    [JsonPropertyName("DefaultValue")]
    public string? DefaultValue { get; set; }
    
    [Json<PERSON>ropertyName("Mandatory")]
    public bool Mandatory { get; set; }
    
    [JsonPropertyName("ContactValue")]
    public bool ContactValue { get; set; }
    
    [JsonPropertyName("TextValue")]
    public string? TextValue { get; set; }
    
    [JsonPropertyName("NumericValue")]
    public decimal? NumericValue { get; set; }
    
    [JsonPropertyName("DateValue")]
    public DateTime? DateValue { get; set; }
    
    [Json<PERSON>ropertyName("ValidationRule")]
    public string? ValidationRule { get; set; }
    
    [JsonPropertyName("ValidationMessage")]
    public string? ValidationMessage { get; set; }
}
