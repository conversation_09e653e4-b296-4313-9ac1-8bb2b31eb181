using System.Text.Json.Serialization;

namespace Bonus.Adapters.LSRetail.Models.Members;

public class Address
{
    [Json<PERSON>ropertyName("Id")]
    public string? Id { get; set; }
    
    [JsonPropertyName("Type")]
    public int Type { get; set; }
    
    [JsonPropertyName("Address1")]
    public string? Address1 { get; set; }
    
    [Json<PERSON>ropertyName("Address2")]
    public string? Address2 { get; set; }
    
    [<PERSON><PERSON><PERSON>roper<PERSON>Name("HouseNo")]
    public string? HouseNo { get; set; }
    
    [JsonPropertyName("City")]
    public string? City { get; set; }
    
    [JsonPropertyName("PostCode")]
    public string? PostCode { get; set; }
    
    [Json<PERSON>ropertyName("StateProvinceRegion")]
    public string? StateProvinceRegion { get; set; }
    
    [Json<PERSON>ropertyName("Country")]
    public string? Country { get; set; }
    
    [Json<PERSON>ropertyName("County")]
    public string? County { get; set; }
    
    [Json<PERSON>roper<PERSON>Name("CellPhoneNumber")]
    public string? CellPhoneNumber { get; set; }
    
    [JsonPropertyName("PhoneNumber")]
    public string? PhoneNumber { get; set; }
}
