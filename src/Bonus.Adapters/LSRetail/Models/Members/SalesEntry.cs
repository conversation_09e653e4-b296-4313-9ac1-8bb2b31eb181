using System.Text.Json.Serialization;

namespace Bonus.Adapters.LSRetail.Models.Members;

public class SalesEntry
{
    [JsonPropertyName("Id")]
    public string? Id { get; set; }
    
    [JsonPropertyName("DocumentNo")]
    public string? DocumentNo { get; set; }
    
    [JsonPropertyName("StoreId")]
    public string? StoreId { get; set; }
    
    [JsonPropertyName("StoreName")]
    public string? StoreName { get; set; }
    
    [JsonPropertyName("TransactionDate")]
    public DateTime TransactionDate { get; set; }
    
    [JsonPropertyName("GrossAmount")]
    public decimal GrossAmount { get; set; }
    
    [JsonPropertyName("NetAmount")]
    public decimal NetAmount { get; set; }
    
    [JsonPropertyName("DiscountAmount")]
    public decimal DiscountAmount { get; set; }
    
    [JsonPropertyName("VatAmount")]
    public decimal VatAmount { get; set; }
    
    [Json<PERSON>ropertyName("PointsEarned")]
    public decimal PointsEarned { get; set; }
    
    [JsonPropertyName("PointsUsed")]
    public decimal PointsUsed { get; set; }
    
    [Json<PERSON>ropertyName("CardNo")]
    public string? CardNo { get; set; }
    
    [JsonPropertyName("PaymentType")]
    public string? PaymentType { get; set; }
    
    [JsonPropertyName("TransactionType")]
    public string? TransactionType { get; set; }
    
    [JsonPropertyName("Status")]
    public int? Status { get; set; }
    
    [JsonPropertyName("Lines")]
    public List<SalesEntryLine> Lines { get; set; } = [];
}
