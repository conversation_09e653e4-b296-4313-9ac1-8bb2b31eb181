using System.Text.Json.Serialization;

namespace Bonus.Adapters.LSRetail.Models.Members;

public class Environment
{
    [JsonPropertyName("Currency")]
    public required Currency Currency { get; set; }
    
    [JsonPropertyName("PasswordPolicy")]
    public string? PasswordPolicy { get; set; }
    
    [JsonPropertyName("Version")]
    public string? Version { get; set; }
}

public class Currency
{
    [JsonPropertyName("Prefix")]
    public string? Prefix { get; set; }
    
    [JsonPropertyName("Postfix")]
    public string? Postfix { get; set; }
    
    [JsonPropertyName("Symbol")]
    public string? Symbol { get; set; }
    
    [JsonPropertyName("RoundOffSales")]
    public decimal RoundOffSales { get; set; }
    
    [JsonPropertyName("RoundOffAmount")]
    public decimal RoundOffAmount { get; set; }
    
    [JsonPropertyName("DecimalSeparator")]
    public string? DecimalSeparator { get; set; }
    
    [JsonPropertyName("ThousandSeparator")]
    public string? ThousandSeparator { get; set; }
    
    [JsonPropertyName("Description")]
    public string? Description { get; set; }
    
    [JsonPropertyName("DecimalPlaces")]
    public int DecimalPlaces { get; set; }
    
    [JsonPropertyName("SaleRoundingMethod")]
    public int SaleRoundingMethod { get; set; }
    
    [JsonPropertyName("AmountRoundingMethod")]
    public int AmountRoundingMethod { get; set; }
    
    [JsonPropertyName("Culture")]
    public string? Culture { get; set; }
    
    [JsonPropertyName("Id")]
    public string? Id { get; set; }
}
