using System.Text.Json.Serialization;

namespace Bonus.Adapters.LSRetail.Models.Members;

public class PublishedOffer
{
    [JsonPropertyName("Id")]
    public string? Id { get; set; }
    
    [JsonPropertyName("Title")]
    public string? Title { get; set; }
    
    [JsonPropertyName("Description")]
    public string? Description { get; set; }
    
    [Json<PERSON>ropertyName("ValidFrom")]
    public DateTime ValidFrom { get; set; }
    
    [JsonPropertyName("ValidTo")]
    public DateTime ValidTo { get; set; }
    
    [JsonPropertyName("OfferType")]
    public string? OfferType { get; set; }
    
    [Json<PERSON>ropertyName("DiscountValue")]
    public decimal DiscountValue { get; set; }
    
    [JsonPropertyName("DiscountType")]
    public string? DiscountType { get; set; }
    
    [JsonPropertyName("MinimumPurchase")]
    public decimal? MinimumPurchase { get; set; }
    
    [Json<PERSON>ropertyName("IsActive")]
    public bool IsActive { get; set; }
    
    [JsonPropertyName("ItemId")]
    public string? ItemId { get; set; }
    
    [Json<PERSON><PERSON>tyName("StoreId")]
    public string? StoreId { get; set; }
}
