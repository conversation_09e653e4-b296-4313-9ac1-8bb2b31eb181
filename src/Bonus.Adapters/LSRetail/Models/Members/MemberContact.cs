using Bonus.Adapters.LSRetail.Models.Baskets;
using Bonus.Adapters.LSRetail.Models.Retail;
using Bonus.Adapters.LSRetail.Models.Setup;

namespace Bonus.Adapters.LSRetail.Models.Members;

public class MemberContact
{
    public required string Id { get; set; }
    
    public string? UserName { get; set; }
    
    public string? Password { get; set; }
    
    public string? FirstName { get; set; }
    
    public string? MiddleName { get; set; }
    
    public string? AuthenticationId { get; set; }
    
    public string? Authenticator { get; set; }
    
    public string? LastName { get; set; }
    
    public string? Email { get; set; }
    
    public string? Name { get; set; }
    
    public int SendReceiptByEMail { get; set; }
    
    public string? Initials { get; set; }
    
    public int Gender { get; set; }
    
    public int MaritalStatus { get; set; }
    
    public string? BirthDay { get; set; }
    
    public Device? LoggedOnToDevice { get; set; }

    public bool Blocked { get; set; }
    
    public string? BlockedBy { get; set; }
    
    public string? BlockedReason { get; set; }
    
    public string? GuestType { get; set; }
    
    public string? AlternateId { get; set; }
    
    public string? ExternalSystem { get; set; }
    
    public required Account? Account { get; set; }
    
    public List<Address> Addresses { get; set; } = [];
    
    public List<Profile> Profiles { get; set; } = [];
    
    public List<OneList> OneLists { get; set; } = [];
    
    public List<Card> Cards { get; set; } = [];
    
    public List<Notification> Notifications { get; set; } = [];
    
    public List<PublishedOffer> PublishedOffers { get; set; } = [];
    
    public List<SalesEntry> SalesEntries { get; set; } = [];
    
    public Environment? Environment { get; set; }
}