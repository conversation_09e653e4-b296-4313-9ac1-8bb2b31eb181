using System.Text.Json.Serialization;

namespace Bonus.Adapters.LSRetail.Models.Members;

public class Notification
{
    [JsonPropertyName("Id")]
    public string? Id { get; set; }
    
    [JsonPropertyName("Title")]
    public string? Title { get; set; }
    
    [JsonPropertyName("Description")]
    public string? Description { get; set; }
    
    [JsonPropertyName("Type")]
    public string? Type { get; set; }
    
    [JsonPropertyName("Status")]
    public string? Status { get; set; }
    
    [Json<PERSON>ropertyName("CreatedAt")]
    public DateTime CreatedAt { get; set; }
    
    [JsonPropertyName("ExpiresAt")]
    public DateTime? ExpiresAt { get; set; }
    
    [Json<PERSON>ropertyName("IsRead")]
    public bool IsRead { get; set; }
}
