using System.Text.Json.Serialization;

namespace Bonus.Adapters.LSRetail.Models.Members;

public class SalesEntryLine
{
    [JsonPropertyName("Id")]
    public string? Id { get; set; }
    
    [JsonPropertyName("LineNo")]
    public int LineNo { get; set; }
    
    [JsonPropertyName("ItemId")]
    public string? ItemId { get; set; }
    
    [JsonPropertyName("ItemName")]
    public string? ItemName { get; set; }
    
    [JsonPropertyName("VariantId")]
    public string? VariantId { get; set; }
    
    [JsonPropertyName("VariantDimension")]
    public string? VariantDimension { get; set; }
    
    [JsonPropertyName("UnitOfMeasure")]
    public string? UnitOfMeasure { get; set; }
    
    [JsonPropertyName("Quantity")]
    public decimal Quantity { get; set; }
    
    [JsonPropertyName("UnitPrice")]
    public decimal UnitPrice { get; set; }
    
    [Json<PERSON>ropertyName("DiscountAmount")]
    public decimal DiscountAmount { get; set; }
    
    [JsonPropertyName("DiscountPercentage")]
    public decimal DiscountPercentage { get; set; }
    
    [JsonPropertyName("NetAmount")]
    public decimal NetAmount { get; set; }
    
    [JsonPropertyName("VatAmount")]
    public decimal VatAmount { get; set; }
    
    [JsonPropertyName("GrossAmount")]
    public decimal GrossAmount { get; set; }
    
    [JsonPropertyName("VatPercentage")]
    public decimal VatPercentage { get; set; }
}
