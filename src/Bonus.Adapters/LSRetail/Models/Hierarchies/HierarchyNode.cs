namespace Bonus.Adapters.LSRetail.Models.Hierarchies;

public class HierarchyNode
{
    public required string Id { get; set; }
    
    public string? Description { get; set; }
    
    public string? ParentNode { get; set; }
    
    public string? ImageId { get; set; }
    
    public int ChildrenOrder { get; set; }
    
    public int Indentation { get; set; }

    public List<HierarchyLeaf> Leafs { get; set; } = [];
    
    public List<HierarchyNode> Nodes { get; set; } = [];
}