using Bonus.Adapters.LSRetail.Enums;
using Bonus.Adapters.LSRetail.Models.Retail;

namespace Bonus.Adapters.LSRetail.Models.Hierarchies;

public class HierarchyLeaf
{
    public required string Id { get; set; }
    
    public string? Description { get; set; }

    public string? HierarchyCode { get; set; }
    
    public string? ParentNode { get; set; }
    
    public string? ImageId { get; set; }
    
    public int SortOrder { get; set; }
    
    public required HierarchyLeafType Type { get; set; }
    
    public List<ItemRecipe> Recipies { get; set; } = [];
}