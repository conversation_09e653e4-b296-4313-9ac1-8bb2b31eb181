using Bonus.Adapters.LSRetail.Enums;

namespace Bonus.Adapters.LSRetail.Models.Items;

public class ItemModifier
{
    public required string Code { get; set; }
    
    public string? SubCode { get; set; }
    
    public string? TriggerCode { get; set; }
    
    public string? Description { get; set; }
    
    public string? ExplanatoryHeaderText { get; set; }
    
    public string? Prompt { get; set; }
    
    public string? UnitOfMeasure { get; set; }
    
    public string? VariantCode { get; set; }
    
    public required ItemModifierType Type { get; set; }
    
    public ItemUsageCategory? UsageCategory { get; set; }
    
    public ItemTriggerFunction? TriggerFunction { get; set; }
    
    public int MinSelection { get; set; }
    
    public int MaxSelection { get; set; }
    
    public int GroupMinSelection { get; set; }
    
    public int GroupMaxSelection { get; set; }
    
    public ItemModifierPriceType? PriceType { get; set; }
    
    public ItemModifierPriceHandling? AlwaysCharge { get; set; }
    
    public decimal AmountPercent { get; set; }
    
    public decimal TimeModifierMinutes { get; set; }
}