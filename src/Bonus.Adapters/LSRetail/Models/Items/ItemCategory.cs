using Bonus.Adapters.LSRetail.Models.Retail;

namespace Bonus.Adapters.LSRetail.Models.Items;

public class ItemCategory
{
    public required string Id { get; set; }
        
    public string? Description { get; set; }

    public ImageView? DefaultImage => Images.Where(x => !string.IsNullOrEmpty(x.Source)).OrderBy(x => x.DisplayOrder).FirstOrDefault();
        
    public List<ImageView> Images { get; set; }  = [];
        
    public List<ProductGroup> ProductGroups { get; set; }  = [];
}