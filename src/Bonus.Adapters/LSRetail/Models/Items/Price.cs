using System.Text.Json.Serialization;

namespace Bonus.Adapters.LSRetail.Models.Items;

public class Price
{
    public required string ItemId { get; set; }
    
    [JsonPropertyName("Amount")]
    public string? FormattedAmount { get; set; }
    
    [JsonPropertyName("Amt")]
    public decimal? Amount { get; set; }
    
    [JsonPropertyName("NetAmt")]
    public decimal? NetAmount { get; set; }
    
    public string? UomId { get; set; }
    
    public string? VariantId { get; set; }
}