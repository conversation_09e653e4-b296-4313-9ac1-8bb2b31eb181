using Bonus.Adapters.LSRetail.Models.Retail;
using Bonus.Shared.Helpers;

namespace Bonus.Adapters.LSRetail.Models.Items;

public class LoyaltyItem
{
    public required string Id { get; set; }
    
    public bool AllowedToSell { get; set; }
    
    public bool BlockDiscount { get; set; }
    
    public bool BlockManualPriceChange { get; set; }
    
    public bool Blocked { get; set; }
    
    public string? Description { get; set; }
    
    /// <summary>
    /// HTML details
    /// </summary>
    public string? Details { get; set; }
    
    public decimal GrossWeight { get; set; }
    
    public bool IsDeleted { get; set; }
    
    public string? ItemCategoryCode { get; set; }
    
    public string? ItemFamilyCode { get; set; }
    
    public string? ItemTrackingCode { get; set; }
    
    public string? ProductGroupId { get; set; }
    
    public string? Price { get; set; }
    
    public string? SalesUomId { get; set; }
    
    public bool ScaleItem { get; set; }
    
    public string? SeasonCode { get; set; }
    
    public UnitOfMeasure? SelectedUnitOfMeasure { get; set; }
    
    public VariantRegistration? SelectedVariant { get; set; }
    
    /// <summary>
    /// Split by ';'
    /// </summary>
    public string? SpecialGroups { get; set; }
    
    public string? TariffNo { get; set; }
    
    public decimal UnitVolume { get; set; }
    
    public decimal UnitsPerParcel { get; set; }
    
    public List<ImageView> Images { get; set; } = [];

    public List<RetailAttribute> ItemAttributes { get; set; } = [];

    public List<ItemLocation> Locations { get; set; } = [];

    public List<ItemModifier> Modifiers { get; set; } = [];
    
    public List<Price> Prices { get; set; } = [];
    
    public List<ItemRecipe> Recipes { get; set; } = [];
    
    public List<UnitOfMeasure> UnitOfMeasures { get; set; } = [];
    
    public List<VariantExt> VariantsExt { get; set; } = [];
    
    public List<VariantRegistration> VariantRegistrations { get; set; } = [];
    
    public ImageView? DefaultImage => Images.Where(x => x.Source.HasValue()).OrderBy(x => x.DisplayOrder).FirstOrDefault();
}