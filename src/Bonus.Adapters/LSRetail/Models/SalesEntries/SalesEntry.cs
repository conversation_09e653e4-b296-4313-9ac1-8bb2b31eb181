namespace Bonus.Adapters.LSRetail.Models.SalesEntries;

/// <summary>
/// https://mobiledemo.lsretail.com/lsomnihelp/html/T_LSRetail_Omni_Domain_DataModel_Base_SalesEntries_SalesEntry.htm
/// </summary>
///
public class SalesEntry
{
    public string Id { get; set; }

    public string CardId { get; set; }

    public string StoreId { get; set; }

    public string StoreName { get; set; }

    public string TerminalId { get; set; }

    public string StaffId { get; set; }

    public string TransactionNumber { get; set; }

    public string ReceiptNumber { get; set; }

    public DateTimeOffset CreateTime { get; set; }

    public DateTimeOffset DocumentRegTime { get; set; }

    public decimal TotalAmount { get; set; }

    public decimal TotalNetAmount { get; set; }

    public decimal TotalDiscount { get; set; }

    public decimal PointsUsedInOrder { get; set; }

    public decimal PointsRewarded { get; set; }

    public decimal PointsBalance { get; set; }

    public List<SalesEntryLine> Lines { get; set; } = [];

    public List<SalesEntryDiscount> Discounts { get; set; } = [];

    public List<SalesEntryPayment> Payments { get; set; } = [];
}
