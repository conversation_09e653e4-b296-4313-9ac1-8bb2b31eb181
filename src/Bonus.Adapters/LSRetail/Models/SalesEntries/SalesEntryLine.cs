using Bonus.Adapters.LSRetail.Models.Retail;

namespace Bonus.Adapters.LSRetail.Models.SalesEntries;

public class SalesEntryLine
{
    public string Id { get; set; }

    public int LineNumber { get; set; }

    public string ItemId { get; set; }
    
    public string? ItemImageId { get; set; }

    public string VariantId { get; set; }

    public string VariantDimension1 { get; set; }

    public string VariantDimension2 { get; set; }

    public string VariantDimension3 { get; set; }

    public string Barcode { get; set; }

    public string ItemDescription { get; set; }

    public string UnitOfMeasureId { get; set; }

    public decimal Quantity { get; set; }

    public decimal Price { get; set; }

    public decimal NetPrice { get; set; }

    public decimal DiscountAmount { get; set; }

    public decimal NetAmount { get; set; }

    public decimal TaxAmount { get; set; }

    public decimal Amount { get; set; }

    public decimal PeriodicDiscountAmount { get; set; }

    public decimal PeriodicDiscountPercent { get; set; }

    public List<SalesEntryDiscountLine> Discounts { get; set; } = new List<SalesEntryDiscountLine>();

    public ImageView Image { get; set; }
}
