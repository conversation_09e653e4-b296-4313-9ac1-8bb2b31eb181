using Bonus.Adapters.LSRetail.Enums;

namespace Bonus.Adapters.LSRetail.Models.Baskets;

public class OneList
{
    public required string Id { get; set; }
    
    public required string CardId { get; set; }

    public DateTimeOffset? CreateDate { get; set; }
    
    public List<OneListLink> CardLinks { get; set; } = [];

    public List<OneListItem> Items { get; set; } = [];
    
    public string? Description { get; set; }
    
    public string? Currency { get; set; }

    public int ExternalType { get; set; } = 0;
    
    public string StoreId { get; set; } = "";
    
    public required ListType ListType { get; set; }
    
    [Obsolete("Do not use this property as it is not used in LS Retail. Always use Description instead.")]
    public string? Name { get; set; }
    
    public decimal TotalAmount { get; set; }
    
    public decimal TotalDiscAmount { get; set; }
    
    public decimal TotalNetAmount { get; set; }
    
    public decimal TotalTaxAmount { get; set; }
}