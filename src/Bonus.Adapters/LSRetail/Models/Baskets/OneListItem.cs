using Bonus.Adapters.LSRetail.Models.Retail;

namespace Bonus.Adapters.LSRetail.Models.Baskets;

/// <summary>
/// https://mobiledemo.lsretail.com/lsomnihelp/html/T_LSRetail_Omni_Domain_DataModel_Loyalty_Baskets_OneListItem.htm
/// </summary>
public class OneListItem
{
    public required string Id { get; set; }
    
    public string? ItemId { get; set; }
    
    public string? ItemDescription { get; set; }
    
    public decimal Amount { get; set; }
    
    public decimal Price { get; set; }
    
    public string? UnitOfMeasureId { get; set; }
    
    public decimal Quantity { get; set; }
    
    public bool IsManualItem { get; set; }
    
    public required DateTimeOffset CreateDate { get; set; }
    
    public ImageView? Image { get; set; }
}