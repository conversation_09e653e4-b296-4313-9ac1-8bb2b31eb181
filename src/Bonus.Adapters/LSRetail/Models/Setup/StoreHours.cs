using Bonus.Adapters.LSRetail.Enums;

namespace Bonus.Adapters.LSRetail.Models.Setup;

public class StoreHours
{
    public required string StoreId { get; set; }

    public StoreHourCalendarType CalendarType { get; set; }

    public int DayOfWeek { get; set; }

    public string? Description { get; set; }

    public DateTime? OpenFrom { get; set; }

    public DateTime? OpenTo { get; set; }

    public string? NameOfDay { get; set; }
}