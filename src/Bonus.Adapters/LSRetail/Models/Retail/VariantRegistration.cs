namespace Bonus.Adapters.LSRetail.Models.Retail;

/// <summary>
/// TODO: CLEANUP: Figure out if we ever use this.
/// </summary>
public class VariantRegistration
{
    public required string ItemId { get; set; }
    
    public string? FrameworkCode { get; set; }
    
    public string? Dimension1 { get; set; }
    
    public string? Dimension2 { get; set; }
    
    public string? Dimension3 { get; set; }
    
    public string? Dimension4 { get; set; }
    
    public string? Dimension5 { get; set; }
    
    public string? Dimension6 { get; set; }
    
    public List<ImageView> Images { get; set; } = [];
}