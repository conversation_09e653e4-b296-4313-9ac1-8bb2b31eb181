namespace Bonus.Adapters.LSRetail.Models.Retail;

public class UnitOfMeasure
{
    public required string Id { get; set; }
    
    /// <summary>
    /// Number of decimals in a unit of measure
    /// </summary>
    public int Decimals { get; set; }
    
    public string? Description { get; set; }
    
    public string? ItemId { get; set; }
    
    public decimal Price { get; set; }

    /// <summary>
    /// Quantity per unit of measure
    /// </summary>
    public decimal QtyPerUom { get; set; }
    
    public string? ShortDescription { get; set; }
}