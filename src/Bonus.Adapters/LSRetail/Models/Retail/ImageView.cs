using System.Text.Json.Serialization;
using Bonus.Adapters.LSRetail.Enums;

namespace Bonus.Adapters.LSRetail.Models.Retail;

public class ImageView
{
    public required string Id { get; set; }

    public string? AvgColor { get; set; }

    public int DisplayOrder { get; set; }

    /// <summary>
    /// JPEG, PNG
    /// </summary>
    public required string Format { get; set; }

    /// <summary>
    /// Base64 string of the image, Empty if StreamURL provided
    /// </summary>
    public string? Image { get; set; }

    [JsonPropertyName("ImgSize")]
    public required ImageSize ImageSize { get; set; }

    /// <summary>
    /// Location of file or URL
    /// </summary>
    public string? Location { get; set; }

    public required LocationType LocationType { get; set; }

    public string? MediaId { get; set; }

    /// <summary>
    /// When locationType is URL, this prop is not empty.
    /// </summary>
    [JsonPropertyName("StreamURL")]
    public string? StreamUrl { get; set; }

    /// <summary>
    /// TODO: LocationType makes no sense. I get streamUrl + locationType Image.
    /// </summary>
    public string Source => LocationType switch
    {
        _ when !string.IsNullOrEmpty(Image) => Image,
        _ when !string.IsNullOrEmpty(StreamUrl) => StreamUrl,
        _ when !string.IsNullOrEmpty(Location) => Location,
        _ => string.Empty
    };
}

public class ImageSize
{
    public required string Id { get; set; }

    public int Height { get; set; }

    public int Width { get; set; }

    /// <summary>
    /// TODO: Return Image where size will not be less than Width or Height
    /// </summary>
    [JsonPropertyName("UseMinHorVerSize")]
    public bool UseMinHorVerSize { get; set; }
}
