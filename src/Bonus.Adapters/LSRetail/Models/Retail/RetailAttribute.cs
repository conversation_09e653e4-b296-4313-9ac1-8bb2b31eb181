using Bonus.Adapters.LSRetail.Enums;

namespace Bonus.Adapters.LSRetail.Models.Retail;

public class RetailAttribute
{
    public required string Code { get; set; }
    
    public string? DefaultValue { get; set; }
    
    public string? Description { get; set; }
    
    public string? LinkField1 { get; set; }
    
    public string? LinkField2 { get; set; }
    
    public string? LinkField3 { get; set; }
    
    public required AttributeLinkType LinkType { get; set; }
    
    public decimal NumericValue { get; set; }
    
    public int Sequence { get; set; }
    
    public required string Value { get; set; }
    
    public required AttributeValueType ValueType { get; set; }

    public List<AttributeOptionValue> OptionValues { get; set; } = [];
}

public class AttributeOptionValue
{
    public required string Value { get; set; }
    
    public required string Code { get; set; }
    
    public int Sequence { get; set; }
}