namespace Bonus.Adapters.LSRetail.Enums;

/// <summary>
/// https://mobiledemo.lsretail.com/lsomnihelp/html/T_LSRetail_Omni_Domain_DataModel_Base_Setup_FeatureFlagName.htm
/// </summary>
public enum FeatureFlagName
{
    None = 100,
    AllowAutoLogoff = 101,
    AutoLogOffAfterMin = 102,
    AllowOffline = 103,
    ExitAfterEachTransaction = 104,
    SendReceiptInEmail = 105,
    ShowNumberPad = 106,
    UseLoyaltySystem = 107,
    PosShowInventory = 108,
    PosInventoryLookup = 109,
    SettingsPassword = 110,
    HideVoidedTransaction = 111,
    AllowCentralLogin = 112,
    
    AllowAnonymousUser = 200,
    AllowShopHome = 201,
    DeviceType = 202,
    CatalogType = 203,
    DefaultWebStore = 204,
    AllowedPaymentWithPOS = 205,
    AllowedPaymentWithCard = 206,
    AllowedPaymentWithLoyalty = 207,
    CardPaymentType = 208,
    CheckStatusTimer = 209,
    TermsAndConditionURL = 210,
    TermsAndConditionVersion = 211,
    OpenGate = 212,
    CloseGate = 213,
    PrivacyPolicyURL = 214,
    PrivacyPolicyVersion = 215,
    ShowCustomerSurvey = 216,
    AddCardBeforeShopping = 217,
    ShowCustomerQrCode = 218,
    ShowPointStatus = 219,
    UseSecurityCheck = 220,
    HidePriceOfItem = 221,
    HideAddCreditCard = 222,
    HideShoppingScreen = 223,
    UseOnlineSearch = 224,
    CurrencyCode = 225,
    AllowedPaymentToCustomerAccount = 226,
    EnableNotifications = 227,
    
    EnablePlatformPayment = 300,
    CardPaymentMethod = 307,
    LsPayServiceIpAddress = 308,
    LsPayServicePort = 309,
    LsPayPluginId = 310,
    LsPayApplePluginId = 311,
    LsPayGooglePluginId = 312,
    
    AudkenniBaseURL = 400,
    AudkenniClientId = 401,
    AudkenniRedirectURL = 402,
    AudkenniSecret = 403,
    AudkenniMessageToUser = 404,
    AudkenniLoginEnabled = 405,
    AudkenniTestUserEnabled = 406,
    AudkenniTestUser = 407,
    AudkenniTestCardId = 408,
    AudkenniTextToMakeAHash = 409,
    GoogleLoginEnabled = 410,
    GoogleIosClientId = 411,
    FacebookLoginEnabled = 420,
    AppleLoginEnabled = 430
}