using System.Text.Json;
using Bonus.Shared.Converters;
using Refit;

namespace Bonus.Adapters.Helpers;

internal static class RefitHelper
{
    internal static JsonSerializerOptions CreateJsonSerializerOptions()
    {
        // From: https://github.com/reactiveui/refit/blob/002280e873b8fd27df33b4d061a4f2a926c7ec91/Refit/SystemTextJsonContentSerializer.cs#L70-L79

        var jsonSerializerOptions = new JsonSerializerOptions();
        jsonSerializerOptions.PropertyNameCaseInsensitive = true;
        // This line is commented out because we should follow the naming conventions of the API, which may not be camelCase.
        // jsonSerializerOptions.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
        jsonSerializerOptions.Converters.Add(new ObjectToInferredTypesConverter());

        // This line is removed as it converts enums to strings, and we don't want that behavior.
        // jsonSerializerOptions.Converters.Add(new JsonStringEnumConverter(JsonNamingPolicy.CamelCase));
        return jsonSerializerOptions;
    }

    public static RefitSettings CreateDefaultRefitSettings()
    {
        var jsonSerializerOptions = CreateJsonSerializerOptions();
        var contentSerializer = new SystemTextJsonContentSerializer(jsonSerializerOptions);
        var refitSettings = new RefitSettings(contentSerializer);
        return refitSettings;
    }
    
    public static RefitSettings CreateLSRetailRefitSettings()
    {
        var jsonSerializerOptions = CreateJsonSerializerOptions();
        // LS Retail uses old date formats, so we need to add converters for Unix epoch dates.
        jsonSerializerOptions.Converters.Add(new UnixEpochDateTimeOffsetConverter());
        jsonSerializerOptions.Converters.Add(new UnixEpochDateTimeConverter()); 
        
        var contentSerializer = new SystemTextJsonContentSerializer(jsonSerializerOptions);

        var refitSettings = new RefitSettings(contentSerializer);

        return refitSettings;
    }
}