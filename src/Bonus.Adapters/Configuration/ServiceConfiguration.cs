using Bonus.Adapters.EasyShop;
using Bonus.Adapters.Helpers;
using Bonus.Adapters.EasyShop.Refit;
using Bonus.Adapters.Expo;
using Bonus.Adapters.HagarId;
using Bonus.Adapters.LSRetail;
using Bonus.Adapters.LSRetail.Refit;
using Bonus.Shared.Configuration.Settings;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Refit;

namespace Bonus.Adapters.Configuration;

public static class ServiceConfiguration
{
    public static void AddBonusAdapters(this IServiceCollection services, IConfiguration configuration)
    {
        var defaultRefitSettings = RefitHelper.CreateDefaultRefitSettings();
        var lsRetailRefitSettings = RefitHelper.CreateLSRetailRefitSettings();

        services.AddRefitClient<ILSRetailApi>(lsRetailRefitSettings)
            .ConfigureHttpClient((serviceProvider, httpClient) =>
            {
                var bonusApiSettings = serviceProvider.GetRequiredService<IOptions<BonusApiSettings>>().Value;
                httpClient.BaseAddress = new Uri(bonusApiSettings.LsRetailBaseAddress);
            })
            .AddHttpMessageHandler<LSRetailHeaderHandler>();
        services.AddTransient<LSRetailHeaderHandler>();

        services.AddTransient<IEasyShopAuthenticationService, EasyShopAuthenticationService>();
        services.AddRefitClient<IEasyShopAuthApi>(defaultRefitSettings)
            .ConfigureHttpClient((serviceProvider, httpClient) =>
            {
                var easyShopApiSettings = serviceProvider.GetRequiredService<IOptions<EasyShopApiSettings>>().Value;
                httpClient.BaseAddress = new Uri(easyShopApiSettings.EasyShopBaseAddress);
            });
        
        services.AddRefitClient<IEasyShopApi>(defaultRefitSettings)
            .ConfigureHttpClient((serviceProvider, httpClient) =>
            {
                var easyShopApiSettings = serviceProvider.GetRequiredService<IOptions<EasyShopApiSettings>>().Value;
                httpClient.BaseAddress = new Uri(easyShopApiSettings.EasyShopBaseAddress);
            })
            .AddHttpMessageHandler<EasyShopHeaderHandler>();
        services.AddTransient<EasyShopHeaderHandler>();

        services.AddRefitClient<IHagarIdApi>(defaultRefitSettings)
            .ConfigureHttpClient((serviceProvider, httpClient) =>
            {
                var hagarIdApiSettings = serviceProvider.GetRequiredService<IOptions<HagarIdApiSettings>>().Value;
                httpClient.BaseAddress = new Uri(hagarIdApiSettings.BaseAddress);
            })
            .AddHttpMessageHandler<HagarIdHeaderHandler>();
        services.AddTransient<HagarIdHeaderHandler>();

        services.AddRefitClient<IExpoPushServiceApi>(defaultRefitSettings)
            .ConfigureHttpClient((serviceProvider, httpClient) =>
            {
                var expoApiSettings = serviceProvider.GetRequiredService<IOptions<ExpoApiSettings>>().Value;
                httpClient.BaseAddress = new Uri(expoApiSettings.BaseAddress);
            });

        services.AddTransient<ILSRetailAdapter, LSRetailAdapter>();
        services.AddTransient<IEasyShopAdapter, EasyShopAdapter>();
    }
}