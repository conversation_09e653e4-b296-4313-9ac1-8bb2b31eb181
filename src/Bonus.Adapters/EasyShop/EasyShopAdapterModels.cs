using Bonus.Adapters.EasyShop.Models;

namespace Bonus.Adapters.EasyShop;

public class EasyShopGetShoppingTripRequest : EasyShopBaseRequest;

public class EasyShopGetFinalCartRequest : EasyShopBaseRequest;

public class EasyShopGetEndShoppingTripStateRequest : EasyShopBaseRequest;

public class EasyShopBeginShoppingTripRequest : EasyShopBaseRequest<ESBeginShoppingTripDto>;

public class EasyShopAddRemoveItemRequest : EasyShopBaseRequest<ESAddRemoveItemDto>;

public class EasyShopChangeTripItemQuantityRequest : EasyShopBaseRequest<ESChangeTripItemQuantityDto>
{
    public required string ItemId { get; set; }
}

public class EasyShopRemoveTripItemRequest : EasyShopBaseRequest
{
    public required string ItemId { get; set; }
}

public class EasyShopEndShoppingTripRequest : EasyShopBaseRequest<ESEndShoppingTripDto>;

public class EasyShopCancelShoppingTripRequest : EasyShopBaseRequest;

// Base request class with body (some don't have body - like GET requests)
public abstract class EasyShopBaseRequest<T> : EasyShopBaseRequest where T : class
{
    public required T Body { get; set; }
}

// Base request class for requests with common headers
public abstract class EasyShopBaseRequest
{
    public required string? CartId { get; set; }
    public required string StoreId { get; set; }
    public required string ShopperIdentifier { get; set; }
    public required string AcceptLanguage { get; set; }
    public required string DeviceId { get; set; }
    public required string CorrelationId { get; set; }
}