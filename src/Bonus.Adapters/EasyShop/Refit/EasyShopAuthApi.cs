using Bonus.Adapters.EasyShop.Models;
using Refit;

namespace Bonus.Adapters.EasyShop.Refit;

public interface IEasyShopAuthApi
{
    [Get("/health")]
    Task<ApiResponse<ESHealthReport>> HealthCheck(CancellationToken cancellationToken);
    
    [Post("/connect/token")]
    [Headers("Content-Type: application/x-www-form-urlencoded")]
    Task<ApiResponse<ESConnectTokenResponse>> GetToken([Body(BodySerializationMethod.UrlEncoded)] ESConnectTokenRequest request, CancellationToken cancellationToken);
}