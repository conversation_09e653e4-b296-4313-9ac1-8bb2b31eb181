using Bonus.Adapters.EasyShop.Models;
using Bonus.Shared.Configuration.Settings;
using Bonus.Shared.Types.Common.Exceptions;
using Microsoft.Extensions.Caching.Hybrid;
using Microsoft.Extensions.Options;

namespace Bonus.Adapters.EasyShop.Refit;

public interface IEasyShopAuthenticationService
{
    Task<string> GetAccessTokenAsync(CancellationToken cancellationToken);
}

public class EasyShopAuthenticationService(IEasyShopAuthApi easyShopApi, IOptions<EasyShopApiSettings> easyShopApiSettings, HybridCache cache) : IEasyShopAuthenticationService
{
    private const string CacheKey = "easyShopApi:accessToken";
    private readonly TimeSpan CacheExpiration = TimeSpan.FromMinutes(5);
    
    public async Task<string> GetAccessTokenAsync(CancellationToken cancellationToken)
    {
        return await cache.GetOrCreateAsync(
            CacheKey,
            async ct =>
            {
                var request = new ESConnectTokenRequest
                {
                    GrantType = easyShopApiSettings.Value.GrantType,
                    ClientId = easyShopApiSettings.Value.ClientId,
                    ClientSecret = easyShopApiSettings.Value.ClientSecret,
                    Scope = easyShopApiSettings.Value.Scope
                };
        
                var tokenResponse = await easyShopApi.GetToken(request, ct);

                if (tokenResponse.IsSuccessful is false)
                {
                    throw new BonusException("Unable to retrieve access token from EasyShop API", tokenResponse.Error);
                }

                return tokenResponse.Content!.AccessToken;
            },
            new HybridCacheEntryOptions()
            {
                Expiration = CacheExpiration,
                LocalCacheExpiration = CacheExpiration,
            },
            cancellationToken: cancellationToken);
    }
}