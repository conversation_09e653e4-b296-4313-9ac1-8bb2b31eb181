using Bonus.Adapters.EasyShop.Models;
using Refit;

namespace Bonus.Adapters.EasyShop.Refit;

public interface IEasyShopApi
{
    /// <summary>
    /// Retrieves an active Shopping Trip from a specific Store that the authenticated user owns.
    /// </summary>
    [Get("/Sites/{siteNumber}/ShoppingTrips/{id}")]
    Task<ApiResponse<ESShoppingTripDto>> GetShoppingTrip(string siteNumber, string? id, [Header(EasyShopHeaderConstants.ShopperIdentifier)] string shopperIdentifier, [Header(EasyShopHeaderConstants.AcceptLanguage)] string acceptLanguage, [Header(EasyShopHeaderConstants.DeviceId)] string deviceId, [Header(EasyShopHeaderConstants.CorrelationId)] string correlationId, CancellationToken cancellationToken);

    /// <summary>
    /// Gets the Final Cart including all discounts and final sale prices.
    /// </summary>
    [Get("/Sites/{siteNumber}/ShoppingTrips/{id}/Final")]
    Task<ApiResponse<ESCartDto>> GetFinalCart(string siteNumber, string id, [Header(EasyShopHeaderConstants.ShopperIdentifier)] string shopperIdentifier, [Header(EasyShopHeaderConstants.AcceptLanguage)] string acceptLanguage, [Header(EasyShopHeaderConstants.DeviceId)] string deviceId, [Header(EasyShopHeaderConstants.CorrelationId)] string correlationId, CancellationToken cancellationToken);

    /// <summary>
    /// Gets the end state of a shopping trip.
    /// </summary>
    [Get("/Sites/{siteNumber}/ShoppingTrips/{id}/End")]
    Task<ApiResponse<ESEndShoppingTripStateDto>> GetEndShoppingTripState(string siteNumber, string id, [Header(EasyShopHeaderConstants.ShopperIdentifier)] string shopperIdentifier, [Header(EasyShopHeaderConstants.AcceptLanguage)] string acceptLanguage, [Header(EasyShopHeaderConstants.DeviceId)] string deviceId, [Header(EasyShopHeaderConstants.CorrelationId)] string correlationId, CancellationToken cancellationToken);
    
    /// <summary>
    /// Begins a Shopping Trip for the authenticated user in a specific Store.
    /// </summary>
    [Post("/Sites/{siteNumber}/ShoppingTrips")]
    Task<ApiResponse<ESShoppingTripDto>> BeginShoppingTrip(string siteNumber, ESBeginShoppingTripDto request, [Header(EasyShopHeaderConstants.ShopperIdentifier)] string shopperIdentifier, [Header(EasyShopHeaderConstants.DeviceId)] string deviceId, [Header(EasyShopHeaderConstants.CorrelationId)] string correlationId, CancellationToken cancellationToken);
    
    /// <summary>
    /// Adds or Removes an item associated with the barcode from an active Shopping Trip.
    /// </summary>
    [Post("/Sites/{siteNumber}/ShoppingTrips/{id}/Items")]
    Task<ApiResponse<ESCartDto>> AddRemoveItem(string siteNumber, string id, ESAddRemoveItemDto request, [Header(EasyShopHeaderConstants.ShopperIdentifier)] string shopperIdentifier, [Header(EasyShopHeaderConstants.AcceptLanguage)] string acceptLanguage, [Header(EasyShopHeaderConstants.DeviceId)] string deviceId, [Header(EasyShopHeaderConstants.CorrelationId)] string correlationId, CancellationToken cancellationToken);
    
    /// <summary>
    /// Changes the Quantity of a Cart Item on an active Shopping Trip.
    /// </summary>
    [Put("/Sites/{siteNumber}/ShoppingTrips/{id}/Items/{itemId}")]
    Task<ApiResponse<ESCartDto>> ChangeTripItemQuantity(string siteNumber, string id, string itemId, ESChangeTripItemQuantityDto request, [Header(EasyShopHeaderConstants.ShopperIdentifier)] string shopperIdentifier, [Header(EasyShopHeaderConstants.AcceptLanguage)] string acceptLanguage, [Header(EasyShopHeaderConstants.DeviceId)] string deviceId, [Header(EasyShopHeaderConstants.CorrelationId)] string correlationId, CancellationToken cancellationToken);
    
    /// <summary>
    /// Removes a Cart Item from an active Shopping Trip.
    /// </summary>
    [Delete("/Sites/{siteNumber}/ShoppingTrips/{id}/Items/{itemId}")]
    Task<ApiResponse<ESCartDto>> RemoveTripItem(string siteNumber, string id, string itemId, [Header(EasyShopHeaderConstants.ShopperIdentifier)] string shopperIdentifier, [Header(EasyShopHeaderConstants.AcceptLanguage)] string acceptLanguage, [Header(EasyShopHeaderConstants.DeviceId)] string deviceId, [Header(EasyShopHeaderConstants.CorrelationId)] string correlationId, CancellationToken cancellationToken);
    
    /// <summary>
    /// Ends an active Shopping Trip in a specific Store that the authenticated user owns.
    /// </summary>
    [Put("/Sites/{siteNumber}/ShoppingTrips/{id}/End")]
    Task<ApiResponse<ESPutEndShoppingTripResponseDto>> EndShoppingTrip(string siteNumber, string id, ESEndShoppingTripDto request, [Header(EasyShopHeaderConstants.ShopperIdentifier)] string shopperIdentifier, [Header(EasyShopHeaderConstants.AcceptLanguage)] string acceptLanguage, [Header(EasyShopHeaderConstants.DeviceId)] string deviceId, [Header(EasyShopHeaderConstants.CorrelationId)] string correlationId, CancellationToken cancellationToken);
    
    /// <summary>
    /// Cancels an active Shopping Trip in a specific Store that the authenticated user owns.
    /// </summary>
    [Delete("/Sites/{siteNumber}/ShoppingTrips/{id}")]
    Task<ApiResponse<ESCancelShoppingTripResponseDto>> CancelShoppingTrip(string siteNumber, string id, [Header(EasyShopHeaderConstants.ShopperIdentifier)] string shopperIdentifier, [Header(EasyShopHeaderConstants.AcceptLanguage)] string acceptLanguage, [Header(EasyShopHeaderConstants.DeviceId)] string deviceId, [Header(EasyShopHeaderConstants.CorrelationId)] string correlationId, CancellationToken cancellationToken);
}

