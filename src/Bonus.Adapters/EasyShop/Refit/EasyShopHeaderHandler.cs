using System.Net.Http.Headers;
using System.Net.Mime;

namespace Bonus.Adapters.EasyShop.Refit;

public class EasyShopHeaderHandler(IEasyShopAuthenticationService authenticationService) : DelegatingHandler
{
    protected override async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
    {
        var accessToken = await authenticationService.GetAccessTokenAsync(cancellationToken);
        request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);

        request.Headers.Accept.Add(new MediaTypeWithQualityHeaderValue(MediaTypeNames.Application.Json));
        
        return await base.SendAsync(request, cancellationToken);
    }
}