using System.Text.Json.Serialization;

namespace Bonus.Adapters.EasyShop.Models;

public class ESConnectTokenRequest
{
    [JsonPropertyName("grant_type")]
    public required string GrantType { get; set; }
    
    [JsonPropertyName("client_id")]
    public required string ClientId { get; set; }
    
    [Json<PERSON>ropertyName("client_secret")]
    public required string ClientSecret { get; set; }
    
    [JsonPropertyName("scope")]
    public required string Scope { get; set; }
}

public class ESConnectTokenResponse
{
    [JsonPropertyName("access_token")]
    public string AccessToken { get; set; } = string.Empty;
    
    [JsonPropertyName("token_type")]
    public string TokenType { get; set; } = string.Empty;
    
    [JsonPropertyName("expires_in")]
    public int ExpiresIn { get; set; }
}