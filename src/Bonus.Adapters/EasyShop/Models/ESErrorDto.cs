namespace Bonus.Adapters.EasyShop.Models;

public class ESErrorDto
{
    /// <summary>
    /// This is most often the HTTP status code returned from the underlying system/module.
    /// </summary>
    public string? Code { get; set; }
    
    /// <summary>
    /// An internal technical message.
    /// </summary>
    public string? InternalMessage { get; set; }
    
    /// <summary>
    /// A user friendly message to display
    /// </summary>
    public string? Message { get; set; }
    
    /// <summary>
    /// If set, will contain a value describing the type of error. Read: ESErrorTypes
    /// </summary>
    public string? Type { get; set; }
}