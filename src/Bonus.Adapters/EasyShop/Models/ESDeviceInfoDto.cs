using System.Text.Json.Serialization;

namespace Bonus.Adapters.EasyShop.Models;

public class ESDeviceInfoDto
{
    /// <summary>
    /// Gets or sets an identifier for the device. (This property is optional. The request header {X-Device-ID} can be used instead.)
    /// WARNING: Please be aware that using this property or the request header will require that all subsequent requests contain {X-Device-ID} with a matching value, otherwise the requests will fail.
    /// </summary>
    [JsonPropertyName("id")]
    public string? Id { get; set; }
    
    /// <summary>
    /// Gets or sets the device model name. (This property is optional.)
    /// </summary>
    [JsonPropertyName("modelName")]
    public string? ModelName { get; set; }
    
    /// <summary>
    /// Gets or sets the operating system running on the device. (This property is optional.)
    /// </summary>
    [JsonPropertyName("operatingSystem")]
    public string? OperatingSystem { get; set; }
    
    /// <summary>
    /// Gets or sets the version of the app running on the device. (This property is optional.)
    /// </summary>
    [JsonPropertyName("appVersion")]
    public string? AppVersion { get; set; }
}