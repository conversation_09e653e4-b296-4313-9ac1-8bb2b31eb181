using System.Text.Json.Serialization;

namespace Bonus.Adapters.EasyShop.Models;

public class ESLocalizedMessageDto
{
    [JsonPropertyName("message")]
    public required string Message { get; set; }
    
    /// <summary>
    /// Gets or sets the language code. (IOS 639-1 language code. The property can be null or empty, indicating the default fallback message.)
    /// </summary>
    [JsonPropertyName("languageCode")]
    public string? LanguageCode { get; set; }
}