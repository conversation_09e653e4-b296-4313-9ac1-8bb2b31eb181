using System.Text.Json.Serialization;
using Bonus.Adapters.EasyShop.Enums;

namespace Bonus.Adapters.EasyShop.Models;

public class ESPutEndShoppingTripResponseDto
{
    [JsonPropertyName("id")]
    public string Id { get; set; }
    
    [JsonPropertyName("endShoppingTripBarcode")]
    public string EndShoppingTripBarcode { get; set; }
   
    [JsonPropertyName("paymentReference")]
    public string? PaymentReference { get; set; }
    
    [JsonPropertyName("shopper")]
    public ESShopperDto Shopper { get; set; }
    
    [JsonPropertyName("cart")]
    public ESCartDto Cart { get; set; }
    
    [JsonPropertyName("paymentAllowedStatus")]
    public PaymentAllowedStatus PaymentAllowedStatus { get; set; }
    
    [JsonPropertyName("paymentBlockedReason")]
    public PaymentBlockedReason? PaymentBlockedReason { get; set; }
    
    [JsonPropertyName("shoppingTripStatus")]
    public ShoppingTripStatus ShoppingTripStatus { get; set; }
}