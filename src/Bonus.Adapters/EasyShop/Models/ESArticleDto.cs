using System.Text.Json.Serialization;
using Bonus.Adapters.EasyShop.Enums;

namespace Bonus.Adapters.EasyShop.Models;

public class ESArticleDto
{
    [JsonPropertyName("description")]
    public required string Description { get; set; }
    
    [JsonPropertyName("price")]
    public required decimal Price { get; set; }
    
    [JsonPropertyName("quantity")]
    public required decimal Quantity { get; set; }
    
    [JsonPropertyName("unitOfMeasure")]
    public required UnitOfMeasureType UnitOfMeasure { get; set; }
    
    [JsonPropertyName("productInformation")]
    public string? ProductInformation { get; set; }
    
    [JsonPropertyName("barcode")]
    public required ESBarcodeDto Barcode { get; set; }
    
    [JsonPropertyName("fixedQuantity")]
    public required bool FixedQuantity { get; set; }
    
    [JsonPropertyName("securityTagged")]
    public required bool SecurityTagged { get; set; }
    
    [JsonPropertyName("ageRequirement")]
    public required int AgeRequirement { get; set; }
    
    [JsonPropertyName("notification")]
    public ESNotificationDto? Notification { get; set; }
    
    [JsonPropertyName("alternativeIdentities")]
    public List<string> AlternativeIdentities { get; set; } = [];
    
    [JsonPropertyName("alternatives")]
    public List<ESArticleAlternativeDto> Alternatives { get; set; } = [];
}