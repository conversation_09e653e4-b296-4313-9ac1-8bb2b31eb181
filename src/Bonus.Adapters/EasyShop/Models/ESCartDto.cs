using System.Text.Json.Serialization;

namespace Bonus.Adapters.EasyShop.Models;

public class ESCartDto
{
    [JsonPropertyName("id")]
    public string Id { get; set; }
    
    [JsonPropertyName("paymentReference")]
    public string? PaymentReference { get; set; }
    
    [JsonPropertyName("paymentAllowed")]
    public bool PaymentAllowed { get; set; }
    
    [JsonPropertyName("total")]
    public decimal Total { get; set; }
    
    [JsonPropertyName("totalDiscount")]
    public decimal TotalDiscount { get; set; }
    
    [JsonPropertyName("items")]
    public List<ESCartItemDto> Items { get; set; } = [];
    
    [JsonPropertyName("vats")]
    public List<ESVatDto> VATs { get; set; } = [];
}