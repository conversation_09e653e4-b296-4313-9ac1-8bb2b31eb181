using System.Text.Json.Serialization;
using Bonus.Adapters.EasyShop.Enums;

namespace Bonus.Adapters.EasyShop.Models;

public class ESCartItemDto
{
    [JsonPropertyName("id")]
    public required string Id { get; set; }
    
    [JsonPropertyName("quantity")]
    public required decimal Quantity { get; set; }
    
    [JsonPropertyName("total")]
    public required decimal Total { get; set; }
    
    [JsonPropertyName("type")]
    public required ESCartItemType Type { get; set; }
    
    [JsonPropertyName("article")]
    public required ESArticleDto Article { get; set; }
    
    [JsonPropertyName("vat")]
    public required ESVatDto VAT { get; set; }
}