using System.Text.Json.Serialization;
using Bonus.Adapters.EasyShop.Enums;

namespace Bonus.Adapters.EasyShop.Models;

public class ESHealthReport
{
    [JsonPropertyName("entries")]
    public Dictionary<string, ESHealthReportEntry> Entries { get; set; } = [];
    
    [JsonPropertyName("status")]
    public HealthStatus Status { get; set; }
}

public class ESHealthReportEntry
{
    public string? Description { get; set; }
    
    [JsonPropertyName("status")]
    public HealthStatus Status { get; set; }
}