using System.Text.Json.Serialization;

namespace Bonus.Adapters.EasyShop.Models;

public class ESShoppingTripDto
{
    [JsonPropertyName("id")]
    public string Id { get; set; }
    
    [JsonPropertyName("shopper")]
    public ESShopperDto Shopper { get; set; }
    
    [JsonPropertyName("cart")]
    public ESCartDto Cart { get; set; }
    
    [JsonPropertyName("endShoppingTripBarcode")]
    public string EndShoppingTripBarcode { get; set; }
}