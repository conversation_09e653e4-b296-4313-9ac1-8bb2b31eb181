namespace Bonus.Adapters.EasyShop.Enums;

public enum BarcodeSymbologyType
{
    Unknown = 0,
    EAN13 = 1,
    EAN8 = 2,
    UPCA = 3,
    UPCE = 4,
    Code3Of9 = 5,
    Code128 = 6,
    CodeInterleaved2Of5 = 7,
    Codabar = 8,
    UCCEAN128 = 9,
    Code93 = 10,
    EANEXT5 = 11,
    EANEXT2 = 12,
    MSI = 13,
    Code11 = 14,
    CodeStandard2Of5 = 15,
    GS1Databar = 16,
    GS1DatabarLimited = 17,
    GS1DatabarExpanded = 18,
    PatchCode = 19,
    PostNet = 20,
    Planet = 21,
    AustralianPost4State = 22,
    RoyalMail4State = 23,
    USPS4State = 24,
    GS1DatabarStacked = 25,
    GS1DatabarExpandedStacked = 26,
    PDF417 = 27,
    MicroPDF417 = 28,
    Datamatrix = 29,
    QR = 30,
    Aztec = 31,
    Maxi = 32,
    MicroQR = 33,
    PharmaCode = 34
}