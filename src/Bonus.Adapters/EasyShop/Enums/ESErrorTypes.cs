namespace Bonus.Adapters.EasyShop.Refit;

public static class ESErrorTypes
{
    /// <summary>
    /// Indicates that there was a problem finding information about a Store.
    /// </summary>
    public const string StoreNotFound = "store-not-found";

    /// <summary>
    /// No article was found matching the provided information.
    /// </summary>
    public const string ArticleNotFound = "article-not-found";

    /// <summary>
    /// The article is not allowed to be sold at this time. This can happen when an article has been recalled.
    /// </summary>
    public const string ArticleSaleBlock = "article-sale-block";

    /// <summary>
    /// The POS owned data associated with the shopping trip can't be found.
    /// </summary>
    public const string CartNotFound = "cart-not-found";

    /// <summary>
    /// The POS owned data has been cancelled/removed but the shopping trip is still active.
    /// </summary>
    public const string CartCancelled = "cart-cancelled";

    /// <summary>
    /// The POS owned data has been marked as completed even though the shopping trip is still active.
    /// </summary>
    public const string CartCompleted = "cart-completed";

    /// <summary>
    /// The POS does not support adding any more items to the cart.
    /// </summary>
    public const string CartSizeExceeded = "cart-size-exceeded";

    /// <summary>
    /// The specified cart item was not found in the cart associated to the shopping trip, this can happen if you try and remove an entire row and it has already been removed.
    /// </summary>
    public const string CartItemNotFound = "cart-item-not-found";

    /// <summary>
    /// An attempt was made to change the quantity of an item to a value that is not valid or supported.
    /// </summary>
    public const string CartItemQuantityInvalid = "cart-item-quantity-invalid";

    /// <summary>
    /// An attempt was made to change the quantity of an item to a value that is too large.
    /// </summary>
    public const string CartItemQuantityExceeded = "cart-item-quantity-exceeded";

    /// <summary>
    /// An attempt was made to change the quantity to a value greater than what is allowed.
    /// </summary>
    public const string CartItemQuantityRestricted = "cart-item-quantity-restricted";

    /// <summary>
    /// Indicates that not all conditions were met when trying to add a specific article, coupon or other type of item to a shopping trip.
    /// This can for example happen if an attempt is made to add an article that is only allowed to be sold during a specific time and the request happens outside of that time window.
    /// </summary>
    public const string ItemRequirementsNotMet = "item-requirements-not-met";

    /// <summary>
    /// The shopper has not been activated or allowed to use the system.
    /// </summary>
    public const string ShopperNotActivated = "shopper-not-activated";

    /// <summary>
    /// No shopper was found matching the provided information.
    /// </summary>
    public const string ShopperNotFound = "shopper-not-found";

    /// <summary>
    /// The shopper is not allowed to use the system.
    /// </summary>
    public const string ShopperSuspended = "shopper-suspended";

    /// <summary>
    /// The shopper is associated with an active shopping trip and the current request is unable to proceed due to this.
    /// </summary>
    public const string ShoppingTripActive = "shopping-trip-active";

    /// <summary>
    /// No shopping trip was found matching the provided information.
    /// </summary>
    public const string ShoppingTripNotFound = "shopping-trip-not-found";

    /// <summary>
    /// The system is currently not allowing any new shopping trips from being started. Shopping trips that are ongoing will be allowed to finish.
    /// </summary>
    public const string ShoppingTripStartDisabled = "shopping-trip-start-disabled";

    /// <summary>
    /// The shopper is associated with a shopping trip that is waiting for payment and the current request is unable to proceed due to this.
    /// </summary>
    public const string ShoppingTripWaitingForPayment = "shopping-trip-waiting-for-payment";

    /// <summary>
    /// A custom message from an external integration point, this can for example be the POS wanting to return a custom message why an article was not allowed to be added to a shopping trip.
    /// </summary>
    public const string Custom = "custom";
}