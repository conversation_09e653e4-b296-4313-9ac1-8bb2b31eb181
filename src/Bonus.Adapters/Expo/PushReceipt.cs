using System.ComponentModel;
using System.Runtime.Serialization;
using System.Text.Json;
using System.Text.Json.Serialization;
using Json.More;

namespace Bonus.Adapters.Expo;

/// <summary>
///     Represents a receipt that is created when Expo delivers a notification to FCM and/or APN. This class contains the
///     payload indicating whether the notification was received or sent successfully.
/// </summary>
public sealed record PushReceipt
{
    /// <summary>
    ///     Specifies the types of errors returned by Expo.
    /// </summary>
    public enum ErrorType
    {
        /// <summary>
        ///     The device can no longer receive notifications and we should stop sending notifications immediately.
        /// </summary>
        [Description("DeviceNotRegistered")]
        DeviceNotRegistered,

        /// <summary>
        ///     The request body is over 4KiB in size. This will be the case when the payload is too large.
        /// </summary>
        [Description("MessageTooBig")]
        MessageTooBig,

        /// <summary>
        ///     We are sending notifications to the device too frequently and need to back off for a bit.
        /// </summary>
        [Description("MessageRateExceeded")]
        MessageRateExceeded
    }

    /// <summary>
    ///     Specifies the status of a push receipt.
    /// </summary>
    public enum PushReceiptStatus
    {
        /// <summary>
        ///     The notification was delivered to FCM/APN, but this status does not guarantee that it's reached the device.
        /// </summary>
        [EnumMember(Value = "ok")]
        Ok,

        /// <summary>
        ///     There was an error with sending the notification to Expo.
        /// </summary>
        [EnumMember(Value = "error")]
        Error
    }

    /// <summary>
    ///     Gets the error details.
    /// </summary>
    [JsonPropertyName("details")]
    public JsonElement? Details { get; init; }

    /// <summary>
    ///     Gets a free-form text describing the error.
    /// </summary>
    [JsonPropertyName("message")]
    public string? Message { get; init; }

    /// <summary>
    ///     Gets the <see cref="PushReceiptStatus" />.
    /// </summary>
    [JsonPropertyName("status")]
    [JsonConverter(typeof(EnumStringConverter<PushReceiptStatus>))]
    public required PushReceiptStatus Status { get; init; }

    /// <summary>
    ///     Represents the details of a push receipt error.
    /// </summary>
    public sealed record ErrorDetails
    {
        /// <summary>
        ///     Gets the <see cref="ErrorType" />.
        /// </summary>
        [JsonPropertyName("error")]
        [JsonConverter(typeof(EnumStringConverter<ErrorType>))]
        public required ErrorType Error { get; init; }
    }
}