using System.ComponentModel;
using System.Text.Json.Serialization;
using Json.More;

namespace Bonus.Adapters.Expo;

/// <summary>
///     Represents the response Expo's Push API returns in case there is an error with a ticket request as a whole.
/// </summary>
public sealed record SendPushNotificationErrorResponse
{
    /// <summary>
    ///     Represents a single error Expo's Push API returns in case there is an error with a ticket request as a whole. For
    ///     errors about individual tickets refer to the details provided in <see cref="PushTicket" />.
    /// </summary>
    public sealed record PushTicketRequestError
    {
        /// <summary>
        ///     Specifies the types of errors that might cause an entire push ticket or push receipt request to fail.
        /// </summary>
        public enum PushTicketRequestErrorType
        {
            Unknown = 0,
        
            /// <summary>
            ///     We are exceeding the limit of 600 notifications per second.
            /// </summary>
            [Description("TOO_MANY_REQUESTS")]
            RateLimited = 1,

            /// <summary>
            ///     We are sending push notifications to multiple projects simultaneously.
            /// </summary>
            [Description("PUSH_TOO_MANY_EXPERIENCE_IDS")]
            TooManyExperiences = 2,

            /// <summary>
            ///     We are trying to send a notification to more than 100 devices in one request.
            /// </summary>
            [Description("PUSH_TOO_MANY_NOTIFICATIONS")]
            TooManyNotifications = 3
        }

        /// <summary>
        ///     Gets the error code returned by Expo. For example, "VALIDATION_ERROR".
        /// </summary>
        [JsonPropertyName("code")]
        [JsonConverter(typeof(EnumStringConverter<PushTicketRequestErrorType>))]
        public required PushTicketRequestErrorType Type { get; init; }

        /// <summary>
        ///     Gets a text that describes the error.
        /// </summary>
        [JsonPropertyName("message")]
        public required string Message { get; init; }
    }
    
    /// <summary>
    ///     Gets an enumerable collection of <see cref="PushTicketRequestError" />.
    /// </summary>
    [JsonPropertyName("errors")]
    public required PushTicketRequestError Errors { get; init; }
}