using System.ComponentModel;
using System.Text.Json.Serialization;
using Json.More;

namespace Bonus.Adapters.Expo;

/// <summary>
///     Represents Expo's response to an individual notification. One ticket is generated per
///     <see cref="PushNotification" /> recipient. To verify whether FCM and/or APN received the notification successfully
///     see <see cref="PushReceipt" />.
/// </summary>
public sealed record PushTicket
{
    /// <summary>
    ///     Specifies the types of errors returned by Expo.
    /// </summary>
    public enum ErrorType
    {
        /// <summary>
        ///     The device can no longer receive notifications and we should stop sending notifications immediately.
        /// </summary>
        [Description("DeviceNotRegistered")]
        DeviceNotRegistered
    }

    /// <summary>
    ///     Specifies the status of a push ticket.
    /// </summary>
    public enum PushTicketStatus
    {
        /// <summary>
        ///     The notification has successfully reached Expo.
        /// </summary>
        [Description("ok")]
        Ok,

        /// <summary>
        ///     There was an error with sending the notification to <PERSON>.
        /// </summary>
        [Description("error")]
        Error
    }

    /// <summary>
    ///     Gets the error details. Present only if <see cref="Status" /> is <see cref="PushTicketStatus.Error" />.
    /// </summary>
    [JsonPropertyName("details")]
    public ErrorDetails? Details { get; init; }

    /// <summary>
    ///     Gets a text describing the error with this ticket. Present only if <see cref="Status" /> is
    ///     <see cref="PushTicketStatus.Error" />.
    /// </summary>
    [JsonPropertyName("message")]
    public string? ErrorMessage { get; init; }

    /// <summary>
    ///     Gets a unique identifier that can be used to retrieve the notification <see cref="PushReceipt" /> later.
    /// </summary>
    [JsonPropertyName("id")]
    public string ReceiptId { get; init; } = default!;

    /// <summary>
    ///     Gets the <see cref="PushTicketStatus" />.
    /// </summary>
    [JsonPropertyName("status")]
    [JsonConverter(typeof(EnumStringConverter<PushTicketStatus>))]
    public required PushTicketStatus Status { get; init; }

    /// <summary>
    ///     Represents the details of a push ticket error.
    /// </summary>
    public sealed record ErrorDetails
    {
        /// <summary>
        ///     Gets the <see cref="ErrorType" />.
        /// </summary>
        [JsonPropertyName("error")]
        [JsonConverter(typeof(EnumStringConverter<ErrorType>))]
        public required ErrorType Error { get; init; }
    }
}