using Refit;

namespace Bonus.Adapters.Expo;

public interface IExpoPushServiceApi
{
    /// <summary>
    ///     Retrieves the receipts for the provided push tickets.
    /// </summary>
    /// <param name="pushReceiptsRequest">The request body.</param>
    /// <returns>An enumerable collection of <see cref="PushReceipt" />s describing the outcome of each push ticket.</returns>
    [Post("/v2/push/getReceipts")]
    Task<ApiResponse<GetPushReceiptsResponse>> GetReceiptsAsync([Body] GetPushReceiptsRequest pushReceiptsRequest);

    /// <summary>
    ///     Sends a single push notification to one or more recipients.
    /// </summary>
    /// <param name="notification">The chunk containing the messages.</param>
    /// <returns>
    ///     The <see cref="PushTicket" /> for the notification or an <see cref="ApiResponse{T}" /> that will contain the
    ///     <see cref="SendPushNotificationErrorResponse" />.
    /// </returns>
    [Post("/v2/push/send")]
    Task<ApiResponse<SendPushNotificationResponse>> SendAsync([Body] PushNotification notification);
    
    /// <summary>
    ///     Sends up to 100 different messages in bulk.
    /// </summary>
    /// <param name="notification">The chunk containing the messages.</param>
    /// <returns>
    ///     The <see cref="PushTicket" /> for the notification or an <see cref="ApiResponse{T}" /> that will contain the
    ///     <see cref="SendPushNotificationErrorResponse" />.
    /// </returns>
    [Post("/v2/push/send")]
    Task<ApiResponse<SendPushNotificationResponse>> SendAsync([Body] IEnumerable<PushNotification> notification);
}