using System.ComponentModel;
using System.Text.Json.Serialization;
using Json.More;

namespace Bonus.Adapters.Expo;

/// <summary>
///     Represents the response of a request issued to get the receipts for a specific push ticket.
/// </summary>
public sealed record GetPushReceiptsResponse
{
    /// <summary>
    ///     Gets a mapping of receipt IDs to their content.
    /// </summary>
    [JsonPropertyName("data")]
    public required Dictionary<string, PushReceipt> Receipts { get; init; }
}

/// <summary>
///     Represents the response Expo's Push API returns in case there is an error with a receipt request as a whole.
/// </summary>
public sealed record PushReceiptRequestErrorResponse
{
    /// <summary>
    ///     Specifies the types of errors that might cause an entire push ticket or push receipt request to fail.
    /// </summary>
    public enum ErrorType
    {
        Unknown = 0,
        
        /// <summary>
        ///     We are exceeding the limit of 600 notifications per second.
        /// </summary>
        [Description("TOO_MANY_REQUESTS")]
        RateLimited = 1,

        /// <summary>
        ///     We are trying to send a notification to more than 100 devices in one request.
        /// </summary>
        [Description("PUSH_TOO_MANY_RECEIPTS")]
        TooManyReceipts = 2,
    }
    
    public sealed record Error
    {
        /// <summary>
        ///     Gets the error code returned by Expo. For example, "VALIDATION_ERROR".
        /// </summary>
        [JsonPropertyName("code")]
        [JsonConverter(typeof(EnumStringConverter<ErrorType>))]
        public required ErrorType Type { get; init; }

        /// <summary>
        ///     Gets a text that describes the error.
        /// </summary>
        [JsonPropertyName("message")]
        public required string Message { get; init; }
    }
    
    /// <summary>
    ///     Gets an enumerable collection of <see cref="Error" />s.
    /// </summary>
    [JsonPropertyName("errors")]
    public required Error Errors { get; init; }
}