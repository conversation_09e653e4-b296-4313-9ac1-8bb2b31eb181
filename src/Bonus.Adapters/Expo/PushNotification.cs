using System.ComponentModel;
using System.Text.Json.Serialization;
using Json.More;

namespace Bonus.Adapters.Expo;

/// <summary>
///     Represents a push notification that is dispatched through Expo's Push API.
/// </summary>
public sealed record PushNotification
{
    /// <summary>
    ///     Specifies the delivery priority of a <see cref="PushNotification" />.
    /// </summary>
    public enum NotificationPriority
    {
        /// <summary>
        ///     Instructs Expo to use whatever the default for the platform is, i.e. <see cref="Normal" /> for Android
        ///     and <see cref="High" /> for iOS.
        /// </summary>
        [Description("default")] Default,

        /// <summary>
        ///     <para>
        ///         Represents the normal priority.
        ///     </para>
        ///     <para>
        ///         This value will not open network connections on sleeping Android devices due to energy preservation so the
        ///         delivery might be delayed.
        ///     </para>
        ///     <para>
        ///         On iOS the notifications are sent at a time that takes into account power
        ///         considerations for the device so multiple messages may be grouped and then burst.
        ///     </para>
        /// </summary>
        [Description("normal")] Normal,

        /// <summary>
        ///     <para>
        ///         Represents the high priority.
        ///     </para>
        ///     <para>
        ///         This value will wake up sleeping Android devices if possible and establish a network connection to deliver the
        ///         message as soon as possible.
        ///     </para>
        ///     <para>
        ///         The message is delivered immediately on iOS as well.
        ///     </para>
        /// </summary>
        [Description("high")] High
    }

    /// <summary>
    ///     Gets the text displayed in the notification.
    /// </summary>
    [JsonPropertyName("body")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? Body { get; init; }

    /// <summary>
    ///     <para>
    ///         Gets the notification's TTL.
    ///     </para>
    ///     <para>
    ///         This represents the number of seconds for which the message may be kept around for redelivery if it hasn't been
    ///         delivered. If set to
    ///         <see langword="null" /> this will default to 0 for APN and 4 weeks for FCM.
    ///     </para>
    /// </summary>
    /// <remarks>
    ///     If set to a too low of a value (such as zero) it may prevent the notification from ever reaching the device on
    ///     Android if the device is in doze mode.
    /// </remarks>
    [JsonPropertyName("ttl")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public int? ExpirationInSeconds { get; init; }

    /// <summary>
    ///     Gets the ID of the notification category that this notification is associated with.
    /// </summary>
    [JsonPropertyName("categoryId")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? NotificationCategoryId { get; init; }

    /// <summary>
    ///     Gets the ID of the notification channel through which to display this notification. If the channel does not exist
    ///     on the device the notification won't be displayed.
    /// </summary>
    [JsonPropertyName("channelId")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? NotificationChannelId { get; init; }

    /// <summary>
    ///     Gets the notifications' <see cref="NotificationPriority" />.
    /// </summary>
    [JsonPropertyName("priority")]
    [JsonConverter(typeof(EnumStringConverter<NotificationPriority>))]
    public NotificationPriority Priority { get; init; } = NotificationPriority.Default;

    /// <summary>
    ///     Gets or sets a collection of Expo push tokens that define the recipients of this message.
    /// </summary>
    [JsonPropertyName("to")]
    public required IList<string> Recipients { get; init; }

    /// <summary>
    ///     Gets the text that is displayed above the <see cref="Body" /> in the notification.
    /// </summary>
    [JsonPropertyName("title")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? Title { get; init; }

    /// <summary>
    ///     <para>Gets or sets an arbitrary object sent to the app as additional data/payload.</para>
    ///     <para>
    ///         The total notification payload
    ///         (including other fields) sent to FCM and APN can be at most 4Kib in size, otherwise they will respond with a
    ///         "Message Too Big" error.
    ///     </para>
    /// </summary>
    [JsonPropertyName("data")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public object? Payload { get; init; }

    [JsonPropertyName("badge")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public int? Badge { get; init; }
}