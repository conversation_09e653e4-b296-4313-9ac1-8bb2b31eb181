using System.Text.Json.Serialization;

namespace Bonus.Adapters.Expo;

/// <summary>
///     Represents the body of a request sent to Expo to get the receipts for push tickets.
/// </summary>
public sealed record GetPushReceiptsRequest
{
    /// <summary>
    ///     Gets an enumerable collection of strings identifying the push tickets you need the receipts for.
    /// </summary>
    [JsonPropertyName("ids")]
    public required IEnumerable<string> TicketIds { get; init; }
}