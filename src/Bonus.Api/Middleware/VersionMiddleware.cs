using Bonus.Api.Constants;
using Bonus.Shared.Helpers;

namespace Bonus.Api.Middleware;

public class VersionMiddleware
{
    private readonly RequestDelegate _next;

    public VersionMiddleware(RequestDelegate next)
    {
        _next = next;
    }

    public async Task Invoke(HttpContext context, ILogger<VersionMiddleware> logger)
    {
        context.Response.Headers.Append(Headers.ServerVersion, GitVersion.CommitSha);

        var loggerState = new Dictionary<string, object>
        {
            ["ServerVersion"] = GitVersion.CommitSha,
            ["ClientVersion"] = context.Request.Headers[Headers.ClientVersion].FirstOrDefault() ?? "-"
        };

        using var scope = logger.BeginScope(loggerState);

        await _next(context);
    }
}