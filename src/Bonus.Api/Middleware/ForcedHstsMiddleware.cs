namespace Bonus.Api.Middleware;

public class ForcedHstsMiddleware
{
    private readonly RequestDelegate _next;

    public ForcedHstsMiddleware(RequestDelegate next)
    {
        _next = next;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        // 31536000 seconds is 365 days
        context.Response.Headers.Append("Strict-Transport-Security", "max-age=31536000; includeSubDomains; preload");
        await _next(context);
    }
}