using System.Globalization;
using Bonus.Shared.Helpers;
using Microsoft.AspNetCore.Localization;

namespace Bonus.Api.Providers;

public class UserRequestCultureProvider : RequestCultureProvider
{
    private readonly CultureInfo _defaultCulture;
    
    public UserRequestCultureProvider(CultureInfo defaultCulture)
    {
        _defaultCulture = defaultCulture;
    }

    public override async Task<ProviderCultureResult> DetermineProviderCultureResult(HttpContext httpContext)
    {
        var culture = httpContext.Request.Headers[Constants.Headers.Culture].ToString();
        
        if (!culture.HasValue())
        {
            return new ProviderCultureResult(_defaultCulture.Name);
        }
        
        var cultureName = culture switch
        {
            "is" => "is-IS",
            "en" => "en-GB",
            _ => _defaultCulture.Name
        };

        return new ProviderCultureResult(cultureName);
    }
}