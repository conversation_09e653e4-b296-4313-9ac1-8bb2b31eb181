using System.Globalization;
using System.Security.Claims;
using Bonus.Api.Helpers;
using Bonus.Shared.Types.Common;
using Bonus.Shared.Types.Common.Exceptions;
using Bonus.Shared.Types.Interfaces;

namespace Bonus.Api.Resolvers;

public class UserClaimsResolver : IBonusUser
{
    public UserClaimsResolver(IHttpContextAccessor httpContextAccessor)
    {
        if (httpContextAccessor.HttpContext?.User.Identity is not ClaimsIdentity identity)
        {
            throw new BonusException(nameof(identity));
        }

        UserId = int.Parse(identity.FindFirst(BonusUserClaimTypes.UserId)?.Value!);
        CardId = identity.FindFirst(BonusUserClaimTypes.CardId)?.Value!;
        DeviceId = identity.FindFirst(BonusUserClaimTypes.DeviceId)?.Value!;
        TermsAccepted = identity.FindFirst(BonusUserClaimTypes.TermsAccepted)?.Value == "true";
        IpAddress = httpContextAccessor.HttpContext.GetClientIpAddress();
    }

    public int UserId { get; }

    public string CardId { get; }

    public string IpAddress { get; }
    
    public string DeviceId { get; }
    
    public bool TermsAccepted { get; }
    
    public string EasyShopCulture => CultureInfo.CurrentCulture.TwoLetterISOLanguageName;
}
