using Bonus.Shared.Types.Interfaces;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;

namespace Bonus.Api.Attributes;

[AttributeUsage(AttributeTargets.Class | AttributeTargets.Method)]
public class ValidTermsAttribute : Attribute, IAuthorizationFilter
{
    private readonly bool _ignore;
    
    public ValidTermsAttribute(bool ignore = false)
    {
        _ignore = ignore;
    }

    public void OnAuthorization(AuthorizationFilterContext context)
    {
        var user = context.HttpContext.RequestServices.GetRequiredService<IBonusUser>();
        if (_ignore is false && user.TermsAccepted is false)
        {
            context.Result = new ForbidResult();
        }
    }
}