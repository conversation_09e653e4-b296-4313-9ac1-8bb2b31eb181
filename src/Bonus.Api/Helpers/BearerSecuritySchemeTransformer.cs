using Microsoft.AspNetCore.OpenApi;
using Microsoft.OpenApi.Models;

namespace Bonus.Api.Helpers;

/// <summary>
/// https://github.com/scalar/scalar/issues/4055#issuecomment-**********
/// </summary>
internal sealed class BearerSecuritySchemeTransformer(Microsoft.AspNetCore.Authentication.IAuthenticationSchemeProvider authenticationSchemeProvider) : IOpenApiDocumentTransformer
{
    public async Task TransformAsync(OpenApiDocument document, OpenApiDocumentTransformerContext context, CancellationToken cancellationToken)
    {
        var authenticationSchemes = await authenticationSchemeProvider.GetAllSchemesAsync();

        if (!authenticationSchemes.Any(authScheme => authScheme.Name == "Bearer"))
        {
            return;
        }
        
        var securitySchemeId = "Bearer";
        
        var requirements = new Dictionary<string, OpenApiSecurityScheme>
        {
            [securitySchemeId] = new()
            {
                Description = "JWT Auth header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
                Name = "Authorization",
                In = ParameterLocation.Header,
                BearerFormat = "JWT",
                Type = SecuritySchemeType.ApiKey,
            }
        };

        document.Components ??= new OpenApiComponents();
        document.Components.SecuritySchemes = requirements;
        
        // Add "Bearer" scheme as a requirement for the API as a whole
        document.SecurityRequirements.Add(new OpenApiSecurityRequirement
        {
            [
                new OpenApiSecurityScheme
                {
                    Reference = new OpenApiReference
                    {
                        Id = securitySchemeId, 
                        Type = ReferenceType.SecurityScheme
                    }
                }
            ] = Array.Empty<string>()
        });
    }
}