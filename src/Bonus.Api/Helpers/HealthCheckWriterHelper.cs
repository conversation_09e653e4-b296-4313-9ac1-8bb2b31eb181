using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Microsoft.Extensions.Diagnostics.HealthChecks;

namespace Bonus.Api.Helpers;

public static class HealthCheckWriterHelper
{
    private static Task WriteResponse(HttpContext context, HealthReport healthReport)
    {
        context.Response.ContentType = "application/json; charset=utf-8";

        var entriesList = healthReport.Entries.Select(x => new HealthEntry
        {
            Name = x.Key,
            Duration = x.Value.Duration,
            Exception = x.Value.Exception is not null ? x.Value.ToString() : null,
            Status = x.Value.Status.ToString()
        })
        .ToList();

        var response = new HealthResponse
        {
            Status = healthReport.Status.ToString(),
            Entries = entriesList,
            TotalDuration = healthReport.TotalDuration
        };

        return context.Response.WriteAsJsonAsync(response);
    }

    public static void MapApiHealthChecks(this WebApplication app)
    {
        app.MapHealthChecks("/health", new HealthCheckOptions
        {
            ResponseWriter = WriteResponse
        });
    }
}

public class HealthResponse
{
    public required string Status { get; set; }

    public List<HealthEntry> Entries { get; set; } = new();

    public TimeSpan TotalDuration { get; set; }
}

public class HealthEntry
{
    public required string Name { get; set; }

    public TimeSpan Duration { get; set; }

    public string? Exception { get; set; }

    public required string Status { get; set; }
}