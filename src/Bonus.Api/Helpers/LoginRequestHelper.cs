using System.Net;
using System.Net.Sockets;

namespace Bonus.Api.Helpers;

public static class LoginRequestHelper
{
    public static string GetClientIpAddress(this HttpContext? httpContext)
    {
        if (httpContext?.Request.Headers.TryGetValue("X-Forwarded-For", out var values) == true
            && IPAddress.TryParse(values.FirstOrDefault(), out var ipAddress))
        {
            return ipAddress?.ToString() ?? string.Empty;
        }

        var remoteIpAddress = httpContext?.Connection.RemoteIpAddress;

        if (remoteIpAddress == null)
        {
            return string.Empty;
        }

        if (IPAddress.IsLoopback(remoteIpAddress))
        {
            remoteIpAddress = Dns.GetHostEntry(Dns.GetHostName())
                .AddressList.FirstOrDefault(ip => ip.AddressFamily == AddressFamily.InterNetwork);
        }

        return remoteIpAddress?.ToString() ?? string.Empty;
    }
}