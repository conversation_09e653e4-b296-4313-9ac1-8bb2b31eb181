using Bonus.Api.Constants;
using Bonus.Services.Handlers.PurchaseHistory;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Bonus.Api.Controllers;

[Authorize]
[ApiController]
[Route("api/purchase-history")]
[ApiExplorerSettings(GroupName = ApiVersions.Current)]
public class PurchaseHistoryController(IMediator mediator)
{
    [HttpGet]
    public async Task<GetPurchaseHistoryResponse> GetPurchaseHistory([FromQuery] GetPurchaseHistoryRequest request, CancellationToken cancellationToken)
    {
        return await mediator.Send(request, cancellationToken);
    }
    
    [HttpGet("details")]
    public async Task<GetPurchaseHistoryDetailsResponse> GetPurchaseHistoryDetails([FromQuery] GetPurchaseHistoryDetailsRequest request, CancellationToken cancellationToken)
    {
        return await mediator.Send(request, cancellationToken);
    }
}