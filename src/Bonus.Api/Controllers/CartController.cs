using Bonus.Api.Constants;
using Bonus.Services.Handlers.Cart;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Bonus.Api.Controllers;

[Authorize]
[ApiController]
[Route("api/cart")]
[ApiExplorerSettings(GroupName = ApiVersions.Current)]
public class CartController(IMediator mediator)
{
    [HttpGet]
    public async Task<GetCartResponse> GetCart([FromQuery] string storeId, [FromQuery] string? cartId, CancellationToken cancellationToken)
    {
        var request = new GetCartRequest
        {
            CartId = cartId,
            StoreId = storeId
        };
        
        return await mediator.Send(request, cancellationToken);
    }
    
    [HttpGet("status")]
    public async Task<GetCartStatusResponse> GetCartStatus([FromQuery] string storeId, [FromQuery] string cartId, CancellationToken cancellationToken)
    {
        var request = new GetCartStatusRequest
        {
            CartId = cartId,
            StoreId = storeId
        };
        
        return await mediator.Send(request, cancellationToken);
    }
    
    [HttpPost("item")]
    public async Task<AddCartItemByBarcodeResponse> AddCartItemByBarcode([FromBody] AddCartItemByBarcodeRequest request, CancellationToken cancellationToken)
    {
        return await mediator.Send(request, cancellationToken);
    }
    
    [HttpPut("item")]
    public async Task<UpdateCartItemResponse> UpdateCartItem([FromBody] UpdateCartItemRequest request, CancellationToken cancellationToken)
    {
        return await mediator.Send(request, cancellationToken);
    }
    
    [HttpDelete("item")]
    public async Task<RemoveCartItemResponse> RemoveCartItem([FromBody] RemoveCartItemRequest request, CancellationToken cancellationToken)
    {
        return await mediator.Send(request, cancellationToken);
    }
    
    [HttpDelete("clear")]
    public async Task<ClearCartResponse> ClearCart([FromBody] ClearCartRequest request, CancellationToken cancellationToken)
    {
        return await mediator.Send(request, cancellationToken);
    }
    
    [HttpPost("checkout")]
    public async Task<CheckoutCartResponse> CheckoutCart([FromBody] CheckoutCartRequest request, CancellationToken cancellationToken)
    {
        return await mediator.Send(request, cancellationToken);
    }
    
    [HttpPost("to-shopping-list")]
    public async Task<ConvertCartToShoppingListResponse> ConvertCartToShoppingList([FromBody] ConvertCartToShoppingListRequest request, CancellationToken cancellationToken)
    {
        return await mediator.Send(request, cancellationToken);
    }
}