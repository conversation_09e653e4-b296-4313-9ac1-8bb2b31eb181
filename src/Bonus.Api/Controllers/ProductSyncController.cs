using Bonus.Services.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

//TODO: only for testing, remove!
namespace Bonus.Api.Controllers;

[ApiController]
[AllowAnonymous]
[Route("api/[controller]")]
public class ProductSyncController(IProductSyncService productSyncService) : ControllerBase
{
    /// <summary>
    /// Sync all products from LSRetail
    /// </summary>
    [HttpPost("sync-all")]
    public async Task<IActionResult> SyncAllProducts(CancellationToken cancellationToken)
    {
        await productSyncService.SyncProductsAsync(cancellationToken);
        return Ok(new { message = "Product sync completed successfully" });
    }
}
