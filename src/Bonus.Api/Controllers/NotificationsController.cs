using Bonus.Api.Constants;
using Bonus.Services.Handlers.Notifications;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Bonus.Api.Controllers;

[Authorize]
[ApiController]
[Route("api/notifications")]
[ApiExplorerSettings(GroupName = ApiVersions.Current)]
public class NotificationsController(IMediator mediator)
{
    [HttpGet]
    public async Task<GetNotificationsResponse> GetNotifications(CancellationToken cancellationToken)
    {
        return await mediator.Send(new GetNotificationsRequest(), cancellationToken);
    }
    
    [HttpGet("{id}")]
    public async Task<GetNotificationByIdResponse> GetNotificationById(int id, CancellationToken cancellationToken)
    {
        var request = new GetNotificationByIdRequest { Id = id };
        return await mediator.Send(request, cancellationToken);
    }
    
    [HttpPost("mark-as-read")]
    public async Task<MarkNotificationAsReadResponse> MarkAsRead([FromBody] MarkNotificationAsReadRequest request, CancellationToken cancellationToken)
    {
        return await mediator.Send(request, cancellationToken);
    }

    /* Only for testing
    [AllowAnonymous]
    [HttpPost("create-notification")]
    public async Task CreateNotification([FromBody] CreateNotificationRequest request, CancellationToken cancellationToken)
    {
        await mediator.Send(request, cancellationToken);
    }
    
    [AllowAnonymous]
    [HttpPost("send-push-notification")]
    public async Task SendPushNotification([FromBody] SendPushNotificationRequest request, CancellationToken cancellationToken)
    {
        await mediator.Send(request, cancellationToken);
    }*/
}