using Bonus.Api.Constants;
using Bonus.Services.Handlers.Images;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Bonus.Api.Controllers;

[AllowAnonymous]
[ApiController]
[Route("api/images")]
[ApiExplorerSettings(GroupName = ApiVersions.Current)]
public class ImagesController(IMediator mediator)
{
    [AllowAnonymous]
    [HttpGet("{id}")]
    public async Task<Stream> GetImage([FromRoute] string id, [FromQuery] int width, [FromQuery] int height, CancellationToken cancellationToken)
    {
        var request = new GetImageRequest
        {
            Id = id,
            Width = width,
            Height = height
        };

        return await mediator.Send(request, cancellationToken);
    }
    
}