using Bonus.Api.Constants;
using Bonus.Services.Handlers.Promotions;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Bonus.Api.Controllers;

[ApiController]
[Route("api/promotions")]
[ApiExplorerSettings(GroupName = ApiVersions.Current)]
public class PromotionsController(IMediator mediator)
{
    [AllowAnonymous]
    [HttpGet("active")]
    public async Task<GetActivePromotionsResponse> GetActivePromotions([FromQuery] GetActivePromotionsRequest request, CancellationToken cancellationToken)
    {
        return await mediator.Send(request, cancellationToken);
    }
}