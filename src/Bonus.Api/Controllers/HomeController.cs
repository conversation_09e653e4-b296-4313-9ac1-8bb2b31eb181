using Bonus.Api.Constants;
using Bonus.Services.Handlers.Home;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Bonus.Api.Controllers;

[Authorize]
[ApiController]
[Route("api/home")]
[ApiExplorerSettings(GroupName = ApiVersions.Current)]
public class HomeController(IMediator mediator)
{
    [HttpPost("wallet-pass")]
    public async Task<CreateWalletPassResponse> CreateWalletPass([FromBody] CreateWalletPassRequest request, CancellationToken cancellationToken)
    {
        return await mediator.Send(request, cancellationToken);
    }

    [HttpGet]
    public async Task<GetHomeDataResponse> GetHomeData(CancellationToken cancellationToken)
    {
        return await mediator.Send(new GetHomeDataRequest(), cancellationToken);
    }
}