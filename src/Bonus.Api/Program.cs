using Bonus.Api.Extensions;
using Bonus.Api.HealthChecks;
using Bonus.Api.Helpers;
using Bonus.Api.Middleware;
using Bonus.Core.Configuration;
using Bonus.Core.Data;
using Bonus.Core.Settings;
using Bonus.Core.Settings.Data;
using Bonus.Services.Configuration;
using Bonus.Shared.Helpers;
using Microsoft.EntityFrameworkCore;

var builder = WebApplication.CreateBuilder(args);
builder.Host.AddSerilog("Api");

builder.Services.AddConfigurationSettings();
builder.Services.AddBonusProblemDetails();

builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();

builder.Services.AddSwagger();

builder.Services.AddDbConfigurationSettingsProviders(builder.Configuration);

builder.Services.AddTokenAuthConfiguration(builder.Configuration);
builder.Services.AddCoreServices(builder.Configuration);
builder.Services.AddApiServices(builder.Configuration);
builder.Services.AddBonusClaimsResolver();
builder.Services.AddLocalizationConfiguration();

var healthChecksBuilder = builder.Services.AddHealthChecks();
healthChecksBuilder.AddDbContextCheck<BonusContext>("Database");
healthChecksBuilder.AddDbContextCheck<BonusSettingsContext>("SecretsProvider");
healthChecksBuilder.AddCheck<EasyShopHealthCheck>("EasyShop");

var app = builder.Build();

using var scope = app.Services.CreateScope();
var ctx = scope.ServiceProvider.GetRequiredService<BonusContext>();
await ctx.Database.MigrateAsync();

app.UseRequestLocalization();

app.UseBonusSwagger();

app.UseMiddleware<VersionMiddleware>();

app.UseCors();
app.UseRouting();

if (!app.Environment.IsDevelopment())
{
    app.UseMiddleware<ForcedHstsMiddleware>();
}

app.UseAuthentication();
app.UseAuthorization();

app.UseExceptionHandler();

app.MapGet("/", () => GitVersion.CommitSha).ExcludeFromDescription();
app.MapControllers().RequireAuthorization();

app.MapApiHealthChecks();

app.Run();