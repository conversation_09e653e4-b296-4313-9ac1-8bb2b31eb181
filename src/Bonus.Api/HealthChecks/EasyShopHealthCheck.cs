using System.Net;
using Bonus.Adapters.EasyShop.Refit;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using HealthStatus = Bonus.Adapters.EasyShop.Enums.HealthStatus;

namespace Bonus.Api.HealthChecks;

public class EasyShopHealthCheck(IEasyShopAuthApi easyShopApi) : IHealthCheck
{
    public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
    {
        try
        {
            var result = await easyShopApi.HealthCheck(cancellationToken);

            if (result.StatusCode is HttpStatusCode.InternalServerError || result.IsSuccessStatusCode is false)
            {
                return HealthCheckResult.Degraded("Connection to EasyShop failed.");
            }

            if (result.Content?.Entries.Any(x => x.Value.Status is not HealthStatus.Healthy) ?? false)
            {
                return HealthCheckResult.Degraded("Connection to EasyShop established. Services degraded.");
            }

            return HealthCheckResult.Healthy("Connection to EasyShop established. Services healthy.");
        }
        catch (Exception)
        {
            return HealthCheckResult.Degraded("Connection to EasyShop failed.");
        }
    }
}