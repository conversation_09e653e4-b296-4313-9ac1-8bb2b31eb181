<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.5" />
		<PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.5" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.5">
		  <PrivateAssets>all</PrivateAssets>
		  <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.Extensions.Diagnostics.HealthChecks.EntityFrameworkCore" Version="9.0.5" />
		<PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.5" />
		<PackageReference Include="Swashbuckle.AspNetCore.SwaggerUI" Version="8.0.0" />
	</ItemGroup>

	<ItemGroup>
	  <ProjectReference Include="..\Bonus.Services\Bonus.Services.csproj" />
	</ItemGroup>

	<ItemGroup>
		<Content Update="appsettings.Development.json" CopyToPublishDirectory="Never" />
	</ItemGroup>

</Project>
