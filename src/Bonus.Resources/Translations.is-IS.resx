<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Auth_PhoneOrSsnRequired" xml:space="preserve">
    <value>Phone number or SSN must be provided. ISK</value>
  </data>
  <data name="Auth_DeviceIdRequired" xml:space="preserve">
    <value>Device ID must be provided. ISK</value>
  </data>
  <data name="Login_UserNotFound" xml:space="preserve">
    <value>User not found. ISK</value>
  </data>
  <data name="Login_AudkenniFailed" xml:space="preserve">
    <value>Audkenni login failed. ISK</value>
  </data>
  <data name="Login_AudkenniSettingsNotFound" xml:space="preserve">
    <value>Required Audkenni settings not found. ISK</value>
  </data>
  <data name="Login_HagarIdFailed" xml:space="preserve">
    <value>HagarId authentication failed. ISK</value>
  </data>
  <data name="Register_EmailRequired" xml:space="preserve">
    <value>Email must be provided. ISK</value>
  </data>
  <data name="Register_AuthIdRequired" xml:space="preserve">
    <value>Authentication ID must be provided. ISK</value>
  </data>
  <data name="Register_ContactCreateFailed" xml:space="preserve">
    <value>Failed to create contact. ISK</value>
  </data>
  <data name="Token_RefreshTokenRequired" xml:space="preserve">
    <value>Refresh token is required. ISK</value>
  </data>
  <data name="Token_RefreshTokenNotFound" xml:space="preserve">
    <value>Refresh token not found. ISK</value>
  </data>
  <data name="Token_RefreshTokenExpired" xml:space="preserve">
    <value>Refresh token expired. ISK</value>
  </data>
  <data name="Token_RefreshTokenUsed" xml:space="preserve">
    <value>Refresh token already used. ISK</value>
  </data>
  <data name="Account_ContactNotFound" xml:space="preserve">
    <value>Account not found. ISK</value>
  </data>
  <data name="Account_UpdateEmailFailed" xml:space="preserve">
    <value>Failed to update email. ISK</value>
  </data>
  <data name="Account_NoAddressesFound" xml:space="preserve">
    <value>No addresses found for the contact. ISK</value>
  </data>
  <data name="Account_UpdatePhoneFailed" xml:space="preserve">
    <value>Failed to update phone number. ISK</value>
  </data>
  <data name="Account_PhoneNotUpdated" xml:space="preserve">
    <value>Phone number was not updated properly. ISK</value>
  </data>
  <data name="ShoppingList_IdRequired" xml:space="preserve">
    <value>Shopping list ID is required. ISK</value>
  </data>
  <data name="ShoppingList_ShareIdRequired" xml:space="preserve">
      <value>Shopping list Share Id is required. ISK</value>
  </data>
  <data name="ShoppingList_NotFound" xml:space="preserve">
    <value>Shopping list not found. ISK</value>
  </data>
  <data name="ShoppingList_ShareNotFound" xml:space="preserve">
      <value>Shopping list share not found. ISK</value>
  </data>
  <data name="ShoppingList_CannotRemoveOwnShare" xml:space="preserve">
      <value>Cannot remove your own share. ISK</value>
  </data>
  <data name="ShoppingList_RemoveShareFailed" xml:space="preserve">
      <value>Failed to remove the share. ISK</value>
  </data>
  <data name="ShoppingList_InvalidContactMethod" xml:space="preserve">
    <value>Invalid contact method. ISK</value>
  </data>
  <data name="ShoppingList_ContactValueRequired" xml:space="preserve">
    <value>Contact value is required. ISK</value>
  </data>
  <data name="ShoppingList_ShareFailed" xml:space="preserve">
    <value>Failed to share shopping list. ISK</value>
  </data>
  <data name="ShoppingList_ItemIdRequired" xml:space="preserve">
    <value>Item ID is required. ISK</value>
  </data>
  <data name="ShoppingList_ItemNotFound" xml:space="preserve">
    <value>Item not found in shopping list. ISK</value>
  </data>
  <data name="ShoppingList_ItemUpdateFailed" xml:space="preserve">
    <value>Failed to update item in shopping list. ISK</value>
  </data>
  <data name="ShoppingList_ItemAddFailed" xml:space="preserve">
    <value>Failed to add item to shopping list. ISK</value>
  </data>
  <data name="ShoppingList_UpdatedItemNotFound" xml:space="preserve">
    <value>Failed to find updated item in the response. ISK</value>
  </data>
  <data name="UserNotAuthenticated" xml:space="preserve">
    <value>User not authenticated. ISK</value>
  </data>
  <data name="Account_DeleteFailed" xml:space="preserve">
    <value>Failed to delete account. ISK</value>
  </data>
  <data name="Account_UpdatedNameFailed" xml:space="preserve">
    <value>Failed to update name. ISK</value>
  </data>
  <data name="ShoppingList_ItemRemoveFailed" xml:space="preserve">
    <value>Failed to remove item from shopping list. ISK</value>
  </data>
  <data name="ShoppingList_ItemNameRequired" xml:space="preserve">
    <value>Item name is required. ISK</value>
  </data>
  <data name="ShoppingList_NameRequired" xml:space="preserve">
    <value>Name is required. ISK</value>
  </data>
  <data name="ShoppingList_CreatedFailed" xml:space="preserve">
    <value>Failed to create shopping list. ISK</value>
  </data>
  <data name="ShoppingList_UpdateFailed" xml:space="preserve">
    <value>Failed to update shopping list. ISK</value>
  </data>
  <data name="Login_HagarIdStartAuthMessage" xml:space="preserve">
    <value>Type in number ISK</value>
  </data>
  <data name="Day_Sunday" xml:space="preserve">
    <value>Sunnudagur</value>
  </data>
  <data name="Day_Tuesday" xml:space="preserve">
    <value>Þriðjudagur</value>
  </data>
  <data name="Day_Monday" xml:space="preserve">
    <value>Mánudagur</value>
  </data>
  <data name="Day_Wednesday" xml:space="preserve">
    <value>Miðvikudagur</value>
  </data>
  <data name="Day_Thursday" xml:space="preserve">
    <value>Fimmtudagur</value>
  </data>
  <data name="Day_Saturday" xml:space="preserve">
    <value>Laugardagur</value>
  </data>
  <data name="Day_Friday" xml:space="preserve">
    <value>Föstudagur</value>
  </data>
  <data name="Cart_AddItemFailed" xml:space="preserve">
    <value>Failed to add item to cart ISK</value>
  </data>
  <data name="Catalogue_CategoryNotFound" xml:space="preserve">
    <value>Flokkur {0} fannst ekki</value>
  </data>
  <data name="Account_AcceptTermsFailed" xml:space="preserve">
    <value>Failed to accept terms ISK</value>
  </data>
  <data name="Communication_UnexpectedError" xml:space="preserve">
    <value>Unexpected error occurred while processing the request ISK</value>
  </data>
  <data name="Cart_AddItemFailed_EasyShop" xml:space="preserve">
    <value>Failed to add item. {0} ISK</value>
  </data>
  <data name="Cart_FailedToGetCart_EasyShop" xml:space="preserve">
    <value>Failed to get cart. {0} ISK</value>
  </data>
  <data name="Cart_Checkout_Mismatch" xml:space="preserve">
    <value>Provided barcode {0} does not match the end shopping trip barcode {1}. ISK</value>
  </data>
  <data name="Cart_CheckoutFailed_EasyShop" xml:space="preserve">
    <value>Failed to checkout cart. {0} ISK</value>
  </data>
  <data name="Cart_FailedToClearCart_EasyShop" xml:space="preserve">
    <value>Failed to clear cart. {0} ISK</value>
  </data>
  <data name="Cart_FailedToCreateCart_EasyShop" xml:space="preserve">
    <value>Failed to create cart. {0} ISK</value>
  </data>
  <data name="Cart_FailedToGetCartStatus_EasyShop" xml:space="preserve">
    <value>Failed to get cart status. {0} ISK</value>
  </data>
  <data name="Cart_RemoveItemFailed_EasyShop" xml:space="preserve">
    <value>Failed to remove cart item. {0} ISK</value>
  </data>
  <data name="Cart_UpdateItemFailed_EasyShop" xml:space="preserve">
    <value>Failed to update cart item. {0} ISK</value>
  </data>
  <data name="Catalogue_ItemNotFound" xml:space="preserve">
    <value>Item with ID {0} not found ISK</value>
  </data>
  <data name="Notification_NotFound" xml:space="preserve">
    <value>Notification not found ISK</value>
  </data>
  <data name="Notification_TitleRequired" xml:space="preserve">
    <value>Notification title is required ISK</value>
  </data>
  <data name="Notification_BodyRequired" xml:space="preserve">
    <value>Notification body is required ISK</value>
  </data>
  <data name="Notification_RecipientsCannotBeEmpty" xml:space="preserve">
    <value>Notification must have recipients ISK</value>
  </data>
  <data name="Notification_FailedToSend" xml:space="preserve">
    <value>Failed to send push notification. {0} ISK</value>
  </data>
  <data name="Auth_SessionIdRequired" xml:space="preserve">
    <value>Session ID must be provided ISK</value>
  </data>
  <data name="Auth_SessionNotFound" xml:space="preserve">
    <value>Session is either missing or expired ISK</value>
  </data>
  <data name="Notification_PushTokenMissing" xml:space="preserve">
    <value>User did not opt into notifications ISK</value>
  </data>
</root>