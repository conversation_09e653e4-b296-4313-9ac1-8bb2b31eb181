using System.Linq.Expressions;
using Bonus.Core.Settings.Data.Entities;
using Bonus.Shared.Data.Entities.Base;
using Microsoft.EntityFrameworkCore;

namespace Bonus.Core.Settings.Data;

public class BonusSettingsContext : DbContext
{
    public const string Schema = "configuration";

    public DbSet<AppSetting> AppSettings => Set<AppSetting>();
    
    private readonly string? _connectionString;

    public BonusSettingsContext(DbContextOptions<BonusSettingsContext> options) 
        : base(options)
    {
    }

    public BonusSettingsContext(string? connectionString)
    {
        _connectionString = connectionString;
    }
    
    protected override void OnConfiguring(DbContextOptionsBuilder options)
    {
        if (options.IsConfigured)
        {
            return;
        }

        options.UseSqlServer(_connectionString);
        options.UseSnakeCaseNamingConvention();
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.HasDefaultSchema(Schema);

        SetSoftDeleteQueryFilter(modelBuilder);
    }
    
    private void SetSoftDeleteQueryFilter(ModelBuilder modelBuilder)
    {
        var softDeleteEntities = typeof(IArchivableEntity).Assembly.GetTypes()
            .Where(x => typeof(IArchivableEntity).IsAssignableFrom(x))
            .Where(x => x.IsClass)
            .Where(x => !x.IsAbstract);

        foreach (var softDeleteEntity in softDeleteEntities)
        {
            modelBuilder.Entity(softDeleteEntity)
                .HasQueryFilter(GenerateQueryFilterLambdaExpression(softDeleteEntity));
        }

        static LambdaExpression GenerateQueryFilterLambdaExpression(Type type)
        {
            // x =>
            var parameter = Expression.Parameter(type, "x");

            // null
            var falseConstant = Expression.Constant(null);

            // x.ArchiveDate
            var propertyAccess = Expression.PropertyOrField(parameter, nameof(IArchivableEntity.ArchivedTime));

            // e.ArchiveDate == null
            var equalExpression = Expression.Equal(propertyAccess, falseConstant);

            // x => e.ArchiveDate == null
            var lambda = Expression.Lambda(equalExpression, parameter);

            return lambda;
        }
    }
}