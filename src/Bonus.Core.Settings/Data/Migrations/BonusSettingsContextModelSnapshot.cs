// <auto-generated />
using System;
using Bonus.Core.Settings.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace Bonus.Core.Settings.Data.Migrations
{
    [DbContext(typeof(BonusSettingsContext))]
    partial class BonusSettingsContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasDefaultSchema("configuration")
                .HasAnnotation("ProductVersion", "9.0.5")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("Bonus.Core.Settings.Data.Entities.AppSetting", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime?>("ArchivedTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("archived_time");

                    b.Property<DateTime>("CreatedTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("created_time");

                    b.Property<string>("Setting")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("setting");

                    b.Property<DateTime>("ValidFrom")
                        .HasColumnType("datetime2")
                        .HasColumnName("valid_from");

                    b.Property<DateTime?>("ValidTo")
                        .HasColumnType("datetime2")
                        .HasColumnName("valid_to");

                    b.Property<string>("Value")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("value");

                    b.Property<int>("Version")
                        .HasColumnType("int")
                        .HasColumnName("version");

                    b.HasKey("Id")
                        .HasName("pk_app_settings");

                    b.ToTable("app_settings", "configuration");
                });
#pragma warning restore 612, 618
        }
    }
}
