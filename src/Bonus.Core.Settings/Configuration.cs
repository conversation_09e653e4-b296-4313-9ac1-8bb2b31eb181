using Bonus.Core.Settings.Data;
using Bonus.Core.Settings.Providers;
using Bonus.Shared.Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Bonus.Core.Settings;

public static class Configuration
{
    public static void AddDbConfigurationSettingsProviders(this IServiceCollection services, IConfigurationManager configuration)
    {
        var connectionsString = configuration.GetConnectionString(Constants.ServicesSettingsContext);

        services.AddDbContext<BonusSettingsContext>(options =>
        {
            options.UseSqlServer(connectionsString);
            options.UseSnakeCaseNamingConvention();
        });

        configuration.Add(new DatabaseConfigurationSource
        {
            ConnectionString = connectionsString
        });
    }
    
    public static void AddDbConfigurationSettingsProviders(this IConfigurationManager configuration)
    {
        var connectionsString = configuration.GetConnectionString(Constants.ServicesSettingsContext);

        configuration.Add(new DatabaseConfigurationSource
        {
            ConnectionString = connectionsString
        });
    }
    
    public static void AddDbSettingsContext(this IServiceCollection services, IConfiguration configuration)
    {
        var connectionsString = configuration.GetConnectionString(Constants.ServicesSettingsContext);

        services.AddDbContext<BonusSettingsContext>(options =>
        {
            options.UseSqlServer(connectionsString);
            options.UseSnakeCaseNamingConvention();
        });
    }

    public static void AddConfigurationSettings(this IServiceCollection services)
    {
        // Get all DbConfigOptionsBase settings through reflection
        var settingsTypes = typeof(DbConfigOptionsBase).Assembly.GetTypes()
            .Where(t => t.IsClass)
            .Where(t => t.IsAbstract is false)
            .Where(t => t.IsSubclassOf(typeof(DbConfigOptionsBase)));

        foreach (var settingsType in settingsTypes)
        {
            // Use reflection to call the AddDbOptions method for each settings type
            var method = typeof(DbConfigOptionsExtensions)
                .GetMethod(nameof(DbConfigOptionsExtensions.AddDbOptions), new[] { typeof(IServiceCollection) })
                ?.MakeGenericMethod(settingsType);
            
            // If the method is found, invoke it with the services collection
            method?.Invoke(null, new object[] { services });
        }
    }
}