using Bonus.Core.Settings.Data;
using Bonus.Core.Settings.Data.Entities;
using Microsoft.Extensions.Configuration;

namespace Bonus.Core.Settings.Providers;

internal class DatabaseConfigurationProvider : ConfigurationProvider, IDisposable
{
    private readonly Timer? _timer;
    private readonly DatabaseConfigurationSource _source;

    public DatabaseConfigurationProvider(DatabaseConfigurationSource appConfigurationSource)
    {
        _source = appConfigurationSource;

        _timer = new Timer
        (
            callback: ReloadSettings,
            dueTime: TimeSpan.FromSeconds(_source.ReloadDelayOnStartInSeconds),
            period: TimeSpan.FromSeconds(_source.ReloadPeriodInSeconds),
            state: null
        );
    }

    public override void Load()
    {
        try
        {
            using var dbContext = new BonusSettingsContext(_source.ConnectionString);

            Data = dbContext.AppSettings
                .Where(x => x.ValidFrom <= DateTime.UtcNow)
                .Where(x => x.ValidTo == null || x.ValidTo > DateTime.UtcNow)
                .GroupBy(x => x.Setting)
                .Select(x => x
                    .OrderByDescending(y => y.Version)
                    .ThenBy(y => y.CreatedTime)
                    .First())
                .ToDictionary<AppSetting, string, string?>(x => x.Setting, x => x.Value, StringComparer.OrdinalIgnoreCase);
        }

        // we do not have access to any kind of logging here
        // we do not want the app to crash if we have no connection string so we just return here
        catch (Exception)
        {
            return;
        }
    }

    private void ReloadSettings(object? state)
    {
        Load();
        OnReload();
    }

    public void Dispose()
    {
        _timer?.Dispose();
    }
}