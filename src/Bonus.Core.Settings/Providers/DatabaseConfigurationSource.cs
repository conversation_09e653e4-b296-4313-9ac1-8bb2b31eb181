using Microsoft.Extensions.Configuration;

namespace Bonus.Core.Settings.Providers;

internal sealed class DatabaseConfigurationSource : IConfigurationSource
{
    public required string? ConnectionString { get; set; }

    public int ReloadPeriodInSeconds { get; set; } = 60;

    // delay for avoiding concurrency issues on app start
    public int ReloadDelayOnStartInSeconds { get; set; } = 10;

    public IConfigurationProvider Build(IConfigurationBuilder builder) => new DatabaseConfigurationProvider(this);
}
