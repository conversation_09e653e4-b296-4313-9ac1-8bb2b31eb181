using Bonus.Core.Data;
using Bonus.Core.Settings.Data;
using Bonus.TestClient.Configuration;
using Bonus.TestClient.Helpers;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

var host = TestClient.Configure();
using var scope = host.Services.CreateScope();
var serviceProvider = scope.ServiceProvider;

var mediator = serviceProvider!.GetRequiredService<IMediator>();
var settingsContext = serviceProvider!.GetRequiredService<BonusSettingsContext>();
var context = serviceProvider!.GetRequiredService<BonusContext>();

await settingsContext.Database.MigrateAsync();

await PopulateBonusSettings.CreateSettings(serviceProvider);
await PopulateStoreMappings.CreateMappings(serviceProvider);
await PopulatePromotions.CreatePromotions(serviceProvider);

Console.WriteLine("Done");