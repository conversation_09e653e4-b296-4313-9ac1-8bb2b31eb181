using Bonus.Core.Data;
using Bonus.Core.Data.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace Bonus.TestClient.Helpers;

public static class PopulateStoreMappings
{
    public static async Task CreateMappings(IServiceProvider serviceProvider)
    {
        var ctx = serviceProvider!.GetRequiredService<BonusContext>();
        await ctx.Database.MigrateAsync();

        var currentEntities = await ctx.StoreMappings.ToListAsync();

        var mappings = new List<StoreMapping>
        {
            new() { LSRetailStoreId = "01", EasyShopStoreId = "185aa8ca-9db6-ed11-b364-0050569c151c" },
        };
        
        /* PROD:
                [Store name] [store number] : [store id]
                Skutuvogur 01:  ea70cd4a-9cde-ed11-b366-0050569c5de3
                Smaratorg 18: 00c0c8b6-9cde-ed11-b366-0050569c5de3
                Spöng 09: 2000f29e-caf3-ed11-b366-0050569c5de3
                Holtagarðar 08: d10af42d-8003-ee11-b367-0050569cc20c
                Norðurtorg 38: 524b4ee6-caf3-ed11-b366-0050569c5de3
                Miðhraun 40: 51a38998-a974-ee11-b370-0050569cc20c
                Ísafjörður 11: a36766cf-bfc6-ee11-b372-0050569cc20c
                Selfoss 16: 113917ea-2c06-ef11-b377-0050569c5de3
                Mosó V13: e0e87519-0175-ef11-b37d-0050569c5de3
                Kauptún V28: 14555e2f-0375-ef11-b37d-0050569c5de3
                Skeifan V36: 246baf7e-0475-ef11-b37d-0050569c5de3
                Norðlingabraut V39: dd932239-0475-ef11-b37d-0050569c5de3
                Naustahverfi V31: f33f4208-b8fd-ef11-b383-0050569c5de3
 */
        
        foreach (var mapping in mappings)
        {
            if (currentEntities.Any(x => x.LSRetailStoreId == mapping.LSRetailStoreId))
            {
                continue;
            }
            
            Console.WriteLine($"Added mapping for \"{mapping.LSRetailStoreId}\" to the database.");
            await ctx.StoreMappings.AddAsync(mapping);
        }
        
        await ctx.SaveChangesAsync();
    }
}