using Bonus.Core.Data;
using Bonus.Core.Data.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace Bonus.TestClient.Helpers;

public static class PopulatePromotions
{
    public static async Task CreatePromotions(IServiceProvider serviceProvider)
    {
        var ctx = serviceProvider.GetRequiredService<BonusContext>();
        await ctx.Database.MigrateAsync();
        
        var promotionsFolder = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data/Promotions");
        
        if (!Directory.Exists(promotionsFolder))
        {
            Console.WriteLine("Promotions folder not found.");
            return;
        }
        
        // Define supported image extensions
        string[] imageExtensions = { ".jpg", ".jpeg", ".png", ".gif", ".bmp" };

        var existingPromotions = await ctx.Promotions
            .Select(p => p.Title)
            .ToListAsync();
        
        foreach (string file in Directory.GetFiles(promotionsFolder))
        {
            string extension = Path.GetExtension(file).ToLowerInvariant();

            var title = Path.GetFileNameWithoutExtension(file);
            
            // Skip if the promotion with the same title already exists
            if (existingPromotions.Contains(title))
            {
                Console.WriteLine($"Promotion with title '{title}' already exists. Skipping file: {Path.GetFileName(file)}");
                continue;
            }
            
            if (imageExtensions.Any(x => x == extension))
            {
                var imageBytes = await File.ReadAllBytesAsync(file);
                var base64String = Convert.ToBase64String(imageBytes);
                
                var promotion = new Promotion
                {
                    Title = title,
                    Description = "Sample promotion description",
                    Base64Image = base64String,
                    LSRetailStoreId = null, // Set to null or a valid store ID if needed
                    ValidFrom = DateTime.UtcNow,
                    ValidTo = null // Example: valid for one month
                };
                
                // Add the promotion to the context
                ctx.Promotions.Add(promotion);
                
                Console.WriteLine($"Added promotion: {title}");
            }
        }
        
        await ctx.SaveChangesAsync();
    }
}