using Bonus.Core.Configuration;
using Bonus.Core.Settings;
using Bonus.Services.Configuration;
using Bonus.Shared.Types.Interfaces;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;

namespace Bonus.TestClient.Configuration;

public static class TestClient
{
    public static IHost Configure()
    {
        IConfigurationManager configManager = new ConfigurationManager();
        configManager.AddJsonFile("appsettings.json").AddEnvironmentVariables();
        configManager.AddDbConfigurationSettingsProviders();
        
        var host = Host.CreateDefaultBuilder()
            .ConfigureAppConfiguration((hostBuilderConfiguration, configurationBuilder) =>
            {
                configurationBuilder.Sources.Clear();

                foreach (var source in configManager.Sources)
                {
                    configurationBuilder.Add(source);
                }
            })
            .ConfigureServices((host, services) =>
            {
                var configuration = host.Configuration;
                
                services.AddConfigurationSettings();
                services.AddDbSettingsContext(configManager);
                
                services.AddCoreServices(configuration);
                services.AddApiServices(configuration);
                
                services.AddScoped<IBonusUser, FakeBonusUser>();
            })
            .AddSerilog("test-client")
            .Build();

        return host;
    }
}

public class FakeBonusUser : IBonusUser
{
    public int UserId => 1;
    
    public string CardId => "CARD000100";
    
    public string IpAddress => "127.0.0.1";
    
    public string DeviceId => "Moberg";
    
    public bool TermsAccepted => true;
    
    public string EasyShopCulture => "en";
}