root = true

[*]

# Async warnings

dotnet_diagnostic.CS1998.severity = suggestion      # CS1998: Async method lacks 'await' operators and will run synchronously
dotnet_diagnostic.CS4014.severity = error           # CS4014: Because this call is not awaited, execution of the current method continues before the call is completed

# Override warnings

dotnet_diagnostic.CS8609.severity = suggestion      # CS8609: Nullability of reference types in return type doesn't match overridden member.

charset = utf-8
end_of_line = lf
insert_final_newline = true
trim_trailing_whitespace = true

indent_style = space
indent_size = 4
tab_width = 4
max_line_length = 120

# Naming conventions
# Class names: PascalCase
# Interfaces: PascalCase with I prefix
# Local variables & method parameters: camelCase
# Private/internal fields: _camelCase
# Public members: PascalCase
# Classes
dotnet_naming_symbols.classes.applicable_kinds = class
dotnet_naming_symbols.classes.applicable_accessibilities = *
dotnet_naming_symbols.classes.required_modifiers =

# Interfaces
dotnet_naming_symbols.interfaces.applicable_kinds = interface
dotnet_naming_symbols.interfaces.applicable_accessibilities = *
dotnet_naming_symbols.interfaces.required_modifiers =

# Local variables
dotnet_naming_symbols.local_variables.applicable_kinds = local
dotnet_naming_symbols.local_variables.applicable_accessibilities = *
dotnet_naming_symbols.local_variables.required_modifiers =

# Method parameters
dotnet_naming_symbols.method_parameters.applicable_kinds = parameter
dotnet_naming_symbols.method_parameters.applicable_accessibilities = *
dotnet_naming_symbols.method_parameters.required_modifiers =

# Private fields
dotnet_naming_symbols.private_fields.applicable_kinds = field
dotnet_naming_symbols.private_fields.applicable_accessibilities = private
dotnet_naming_symbols.private_fields.required_modifiers =

# Public members (fields, properties, methods)
dotnet_naming_symbols.public_members.applicable_kinds = property, method, event, field
dotnet_naming_symbols.public_members.applicable_accessibilities = public
dotnet_naming_symbols.public_members.required_modifiers =

# -----------------------------
# Naming styles
# -----------------------------

# PascalCase style
dotnet_naming_style.pascal_case.capitalization = pascal_case
dotnet_naming_style.pascal_case.required_prefix =
dotnet_naming_style.pascal_case.required_suffix =

# CamelCase style
dotnet_naming_style.camel_case.capitalization = camel_case
dotnet_naming_style.camel_case.required_prefix =
dotnet_naming_style.camel_case.required_suffix =

# Private field style (_camelCase)
dotnet_naming_style.private_field_style.capitalization = camel_case
dotnet_naming_style.private_field_style.required_prefix = _
dotnet_naming_style.private_field_style.required_suffix =

# Interface style (I + PascalCase)
dotnet_naming_style.interface_style.capitalization = pascal_case
dotnet_naming_style.interface_style.required_prefix = I
dotnet_naming_style.interface_style.required_suffix =

# -----------------------------
# Naming rules
# -----------------------------

# Class names
dotnet_naming_rule.class_names_should_be_pascal_case.symbols = classes
dotnet_naming_rule.class_names_should_be_pascal_case.style = pascal_case
dotnet_naming_rule.class_names_should_be_pascal_case.severity = warning

# Interface names
dotnet_naming_rule.interface_names_should_start_with_I.symbols = interfaces
dotnet_naming_rule.interface_names_should_start_with_I.style = interface_style
dotnet_naming_rule.interface_names_should_start_with_I.severity = warning

# Local variables
dotnet_naming_rule.local_variables_should_be_camel_case.symbols = local_variables
dotnet_naming_rule.local_variables_should_be_camel_case.style = camel_case
dotnet_naming_rule.local_variables_should_be_camel_case.severity = warning

# Method parameters
dotnet_naming_rule.method_parameters_should_be_camel_case.symbols = method_parameters
dotnet_naming_rule.method_parameters_should_be_camel_case.style = camel_case
dotnet_naming_rule.method_parameters_should_be_camel_case.severity = warning

# Private fields
dotnet_naming_rule.private_fields_should_have_underscore_prefix.symbols = private_fields
dotnet_naming_rule.private_fields_should_have_underscore_prefix.style = private_field_style
dotnet_naming_rule.private_fields_should_have_underscore_prefix.severity = warning

# Public members
dotnet_naming_rule.public_members_should_be_pascal_case.symbols = public_members
dotnet_naming_rule.public_members_should_be_pascal_case.style = pascal_case
dotnet_naming_rule.public_members_should_be_pascal_case.severity = warning

# Spacing
space_around_operators = true
space_after_comma = true
space_before_method_parentheses = false

# Braces
csharp_new_line_before_open_brace = all
csharp_preserve_single_line_blocks = false

# Blank lines
keep_blank_lines_between_members = 1
keep_blank_lines_in_code = 2
keep_blank_lines_before_return = 1

# Statements per line
csharp_statement_placement = one_per_line

# LINQ / Where statement conventions
# Split chained methods onto separate lines
csharp_linq_split_chained_methods = true
csharp_linq_use_where_instead_of_condition_in_single_or_first = true
csharp_linq_use_meaningful_query_variable_names = true
csharp_linq_use_async_methods = true
csharp_linq_use_foreach_over_linq_foreach = true

# Object initializers
csharp_object_initializer_braces_on_new_line = true
csharp_object_initializer_assign_all_properties = true

# Implicit typing
csharp_use_var_for_locals = true

# Foreach and loop rules
csharp_use_foreach_over_for = true
csharp_avoid_initializing_collections_in_loops = true

# File / namespace layout
csharp_file_scoped_namespaces = true
csharp_match_namespace_to_folder_structure = true
csharp_using_directives_outside_namespace = true
csharp_sort_system_using_first = true
csharp_use_simple_using_declaration = true

# Misc
csharp_avoid_else_when_possible = true
csharp_extract_complicated_expression_to_variable = true
csharp_prefer_null_coalescing = true
csharp_prefer_ternary_over_if_else = true
csharp_prefer_simplified_conditional_expressions = true
csharp_prefer_simplified_interpolated_strings = true
csharp_fields_should_not_be_prefaced_with_this = true

# Control blank lines
# Never allow 2 consecutive empty lines
dotnet_style_allow_multiple_blank_lines = false:suggestion

# Keep single blank line between type members and methods
dotnet_separate_type_members = true:suggestion
dotnet_separate_method_groups = true:suggestion

# Leave one blank line before return statements
csharp_blank_lines_before_return = 1


# LINQ conventions
# -----------------

# Place each chained method call on a new line
csharp_preserve_single_line_statements = false
csharp_new_line_before_members = true
csharp_indent_braces = true
csharp_indent_block_contents = true

# Continuation indent for chained method calls (4 spaces)
csharp_indent_continuation = 4

# Custom rule (naming / semantic) to remind to split multiple && into multiple Where
dotnet_linq_split_multiple_and_in_where = true
dotnet_linq_use_where_instead_of_condition_in_first_or_single = true
dotnet_linq_use_meaningful_query_variable_names = true
dotnet_linq_use_foreach_over_linq_foreach = true