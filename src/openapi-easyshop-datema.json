{"openapi": "3.0.1", "info": {"title": "EasyShop Gateway Server API", "description": "## Overview\r\n\r\nThe EasyShop Gateway API is used to perform shopping trips with non-retail-owned devices, in other words a smartphone app, a website, etc.\r\n\r\n### Security\r\n\r\nThe API uses an HTTP authentication scheme called **Bearer authentication** (also called **Token authentication**). The access token must be sent in the **Authorization** header when making requests to protected operations.\r\n\r\n```http\r\nAuthorization: Bearer <token>\r\n```\r\n\r\n#### Supported authorization flows\r\n\r\nThese are the currently supported authorization flows:\r\n- Client Credentials\r\n- JWT Bearer\r\n\r\n### Content type\r\n\r\nThe API uses JSON as the content type for requests and responses. Use the **Content-Type** request header with the value: application/json.\r\n\r\n```http\r\nContent-Type: application/json\r\n```\r\n\r\n### Localization\r\n\r\nThe API and underlying components have support for localization. The list of supported languages will differ from installation to installation. In order to request a response be localized the **Accept-Language** header should be used. The value should be a valid two letter ISO 639-1 language code.\r\n\r\n[List of ISO 639-1 codes](https://en.wikipedia.org/wiki/List_of_ISO_639-1_codes)\r\n\r\n```http\r\nAccept-Language: sv\r\n```\r\n\r\n\r\n## Authorization\r\n\r\n### Getting an access token (Client credentials flow)\r\n\r\nTo get an access token use the <a href=\"#/Token/RequestToken\" target=\"_blank\" class=\"swagger-internal-link\">POST /connect/token</a> operation.\r\n\r\nThe supported grant type is:\r\n- client_credentials\r\n\r\n#### Client credentials grant type\r\n\r\nWhen using grant type client_credentials, you will have to provide the following values:\r\n- grant_type (\"client_credentials\")\r\n- client_id\r\n- client_secret\r\n- scope (\"openid\" is the only and optional supported value for this grant type)\r\n\r\n### JWT Bearer\r\n\r\nWhen using an external JWT Authority, the Client Credentials flow gets disabled. We currently do not support having them enabled at the same time.\r\n\r\n#### Shopper Identifier Claim\r\n\r\nIt is recommended that the shopper identifier is included in the token as a claim. The name of the claim does not matter as it is configurable. If it is not possible to have the shopper identifier as a claim then the request header X-Shopper-Identifier is required.\r\n\r\n\r\n## Shopping trips\r\n\r\nAll the shopping trip operations require an access token or JWT.\r\n\r\n> Optional request header X-Shopper-Identifier is required when using Client Credentials or JWT without a shopper identifier claim.\r\n\r\n### Starting a shopping trip\r\n\r\nTo start a shopping trip use the <a href=\"#/ShoppingTrip/BeginShoppingTrip\" target=\"_blank\" class=\"swagger-internal-link\">POST /Sites/{siteNumber}/ShoppingTrips</a> operation.\r\n\r\n> You will need to know a site identifier (Site refers to a Store).\r\n\r\n> The value \"DEMO\" can be used as a site identifier.\r\n\r\n#### DeviceInfo and X-Device-ID\r\n\r\nAn optional data structure exists where information about a device can be passed along. Each field in the data structure is optional.\r\n\r\nIf the id field or the **X-Device-ID** request header is used, then be aware that all future requests for the shopping trip have to contain the **X-Device-ID** request header with the same value. Failure to do so will result in requests failing (some non-destructive requests might work as they skip the device id validation).\r\n\r\nIf you have no specific need to pass in an id value, then avoid doing so and do not use the **X-Device-ID** request header.\r\n\r\nBe aware that any value passed in as the id/X-Device-ID could be GDPR sensitive.\r\n\r\n### Retrieving an active shopping trip\r\n\r\nTo retrieve an active shopping trip use the <a href=\"#/ShoppingTrip/GetShoppingTrip\" target=\"_blank\" class=\"swagger-internal-link\">GET /Sites/{siteNumber}/ShoppingTrips/{id}</a> operation; if the shopping trip id is unknown, a value of 0 can be used.\r\n\r\n### Adding an item to a shopping trip\r\n\r\nTo add an item use the <a href=\"#/ShoppingTrip/AddRemoveItem\" target=\"_blank\" class=\"swagger-internal-link\">POST /Sites/{siteNumber}/ShoppingTrips/{id}/Items</a> operation.\r\n\r\nA positive quantity value indicates that it is an add request.\r\n\r\nThe request will try and add an item associated with the barcode. Depending on what type of item it is, the result might be that a new item is added to the shopping trip or an existing item has its quantity changed.\r\n\r\n> The same operation is used when removing an item from a shopping trip.\r\n\r\n> The quantity value is currently only used to indicate whether it is an add or remove request.\r\n\r\n#### Item alternatives\r\n\r\nSome POS vendors support configuring alternative items, it is sometimes referred to as crate handling. After adding an item, investigate the Cart.Items.Article.Alternatives structure for alternative items in the form of barcodes and descriptions.\r\n\r\nUse the <a href=\"#/ShoppingTrip/RemoveTripItem\" target=\"_blank\" class=\"swagger-internal-link\">DELETE /Sites/{siteNumber}/ShoppingTrips/{id}/Items/{itemId}</a> operation to remove the cart item and then add the suggested item using the <a href=\"#/ShoppingTrip/AddRemoveItem\" target=\"_blank\" class=\"swagger-internal-link\">POST /Sites/{siteNumber}/ShoppingTrips/{id}/Items</a> operation.\r\n\r\n\r\n### Changing the quantity of an item on a shopping trip\r\n\r\nTo change the quantity of an item use the <a href=\"#/ShoppingTrip/ChangeTripItemQuantity\" target=\"_blank\" class=\"swagger-internal-link\">PUT /Sites/{siteNumber}/ShoppingTrips/{id}/Items/{itemId}</a> operation.\r\n\r\n### Removing an item from a shopping trip\r\n\r\nTo remove an item from a shopping trip use the <a href=\"#/ShoppingTrip/AddRemoveItem\" target=\"_blank\" class=\"swagger-internal-link\">POST /Sites/{siteNumber}/ShoppingTrips/{id}/Items</a> operation.\r\n\r\nA negative quantity value indicates that it is a remove request.\r\n\r\nThe request will try and remove an item associated with the barcode. Depending on what type of item it is, the result might be that an item has its quantity changed or is removed from the shopping trip. \r\n\r\n> The same operation is used when adding an item to a shopping trip.\r\n\r\n> The quantity value is currently only used to indicate whether it is an add or remove request.\r\n\r\n### Removing a cart item from a shopping trip\r\n\r\nTo remove a specific cart item use the <a href=\"#/ShoppingTrip/RemoveTripItem\" target=\"_blank\" class=\"swagger-internal-link\">DELETE /Sites/{siteNumber}/ShoppingTrips/{id}/Items/{itemId}</a> operation.\r\n\r\n### Retrieving a final cart for a shopping trip\r\n\r\nTo retrieve a final cart for a shopping trip use the <a href=\"#/ShoppingTrip/GetFinalCart\" target=\"_blank\" class=\"swagger-internal-link\">GET /Sites/{siteNumber}/ShoppingTrips/{id}/Final</a> operation.\r\n\r\nSome POS vendors do not support discounts or other offers during an active shopping trip, due to various reasons. This operation is to be able to request a cart where the POS has tried to factor in discounts and offers in order to return the final sale cart.\r\n\r\n> This operation does not affect and/or change the shopping trip, it is merely an information request operation.\r\n\r\n### Ending a shopping trip\r\n\r\nTo end a shopping trip use the <a href=\"#/ShoppingTrip/EndShoppingTrip\" target=\"_blank\" class=\"swagger-internal-link\">PUT /Sites/{siteNumber}/ShoppingTrips/{id}/End</a> operation.\r\n\r\nThis operation is used to initiate the checkout flow and prevent further changes to a shopping trip. Depending on your configured environment, payment might be blocked due to a control having been generated; information is available in the response data structure.\r\n\r\nPlease refer to your additional documentation describing the flow of events for your environment when ending a shopping trip.\r\n\r\n> If the request resulted in a timeout and subsequent calls fail, then use the GET version of this operation, mentioned below, to retrieve information about the end state of a shopping trip.\r\n\r\n### Retrieving end state of a shopping trip\r\n\r\nTo retrieve information about the end state of a shopping trip use the <a href=\"#/ShoppingTrip/GetEndShoppingTripState\" target=\"_blank\" class=\"swagger-internal-link\">GET /Sites/{siteNumber}/ShoppingTrips/{id}/End</a> operation.\r\n\r\nThis operation is used to retrieve information about a shopping trip's end state. The response data structure contains the status of the shopping trip, if payment is allowed, blocked or has not yet been decided due to a control needing to be performed (status in this case pending), as well a payment reference that might be required during checkout.\r\n\r\n### Cancelling a shopping trip\r\n\r\nTo cancel a shopping trip use the <a href=\"#/ShoppingTrip/CancelShoppingTrip\" target=\"_blank\" class=\"swagger-internal-link\">DELETE /Sites/{siteNumber}/ShoppingTrips/{id}</a> operation.\r\n\r\n\r\n## Example call order\r\n\r\n- Get an access token using the <a href=\"#/Token/RequestToken\" target=\"_blank\" class=\"swagger-internal-link\">POST /connect/token</a> operation.\r\n  - See the Authorization section for details.\r\n- Start a shopping trip using the <a href=\"#/ShoppingTrip/BeginShoppingTrip\" target=\"_blank\" class=\"swagger-internal-link\">POST /Sites/{siteNumber}/ShoppingTrips</a> operation.\r\n  - If the request fails saying that an active shopping trip already exist, then use the <a href=\"#/ShoppingTrip/GetShoppingTrip\" target=\"_blank\" class=\"swagger-internal-link\">GET /Sites/{siteNumber}/ShoppingTrips/{id}</a> operation passing in 0 as the shopping trip id.\r\n  - If the request fails indicating a conflict then use the <a href=\"#/ShoppingTrip/GetEndShoppingTripState\" target=\"_blank\" class=\"swagger-internal-link\">GET /Sites/{siteNumber}/ShoppingTrips/{id}/End</a> operation to retrive the shopping trip regardless of its current state, it could indicate that there shopper has a shopping trip that is waiting for payment or blocked waiting for staff intervention.\r\n- Add and Remove items using the <a href=\"#/ShoppingTrip/AddRemoveItem\" target=\"_blank\" class=\"swagger-internal-link\">POST /Sites/{siteNumber}/ShoppingTrips/{id}/Items</a> operation.\r\n  - See the Shopping trips section for more details and other possible interactions.\r\n- Tell the system that the user has finished modifying the shopping trip by using the <a href=\"#/ShoppingTrip/EndShoppingTrip\" target=\"_blank\" class=\"swagger-internal-link\">PUT /Sites/{siteNumber}/ShoppingTrips/{id}/End</a> operation.\r\n  - This operation is used to initiate the checkout flow and prevent further changes to a shopping trip. Depending on your configured environment, payment might be blocked due to a control having been generated; information is available in the response data structure. \r\n  - Please refer to your additional documentation describing the flow of events for your environment when ending a shopping trip.\r\n- (Optional) Retrieve end state of a shopping trip using the <a href=\"#/ShoppingTrip/GetEndShoppingTripState\" target=\"_blank\" class=\"swagger-internal-link\">GET /Sites/{siteNumber}/ShoppingTrips/{id}/End</a> operation.\r\n  - The request to initiate checkout might have timed out and subsequent calls return information that the checkout has already been initiated, this request can then be used to retrieve information.\r\n\r\n## Error handling\r\n\r\nFor non-successful requests, an error payload will often be returned in the response body. The data contained within the error is from the underlying system/module that had the error. The error structure will contain the following data:\r\n- Code - This is most often the HTTP status code returned from the underlying system/module.\r\n- Message - A user friendly message to display.\r\n- InternalMessage - An internal technical message.\r\n- Type - If set, will contain a value describing the type of error (see possible values below).\r\n\r\n### Type values\r\n\r\n- \"store-not-found\" - Indicates that there was a problem finding information about a Store.\r\n\r\n- \"article-not-found\" - No article was found matching the provided information.\r\n- \"article-sale-block\" - The article is not allowed to be sold at this time. This can happen when an article has been recalled.\r\n\r\n- \"cart-not-found\" - The POS owned data associated with the shopping trip can't be found.\r\n- \"cart-cancelled\" - The POS owned data has been cancelled/removed but the shopping trip is still active.\r\n- \"cart-completed\" - The POS owned data has been marked as completed even though the shopping trip is still active.\r\n- \"cart-size-exceeded\" - The POS does not support adding any more items to the cart.\r\n\r\n- \"cart-item-not-found\" - The specified cart item was not found in the cart associated to the shopping trip, this can happen if you try and remove an entire row and it has already been removed.\r\n- \"cart-item-quantity-invalid\" - An attempt was made to change the quantity of an item to a value that is not valid or supprted.\r\n- \"cart-item-quantity-exceeded\" - An attempt was made to change the quantity of an item to a value that is too large.\r\n- \"cart-item-quantity-restricted\" - An attempt was made to change the quantity to value greater than what is allowed.\r\n\r\n- \"item-requirements-not-met\" - Indicates that not all conditions were met when trying to add a specific article, coupon or other type of item to a shopping trip. This can for example happen if an attempt is made to add an article that is only allowed to be sold during a specific time and the request happens outside of that time window.\r\n\r\n- \"shopper-not-activated\" - The shopper has not been activated or allowed to use the system.\r\n- \"shopper-not-found\" - No shopper was found matching the provided information.\r\n- \"shopper-suspended\" - The shopper is not allowed to use the system.\r\n\r\n- \"shopping-trip-active\" - The shopper is associated with an active shopping trip and the current request is unable to proceed due to this. \r\n- \"shopping-trip-not-found\" - No shopping trip was found matching the provided information.\r\n- \"shopping-trip-start-disabled\" - The system is currently not allowing any new shopping trips from being started. Shopping trips that are ongoing will be allowed to finish.\r\n- \"shopping-trip-waiting-for-payment\" - The shopper is associated with a shopping trip that is waiting for payment and the current request is unable to proceed due to this.\r\n\r\n- \"custom\" - A custom message from an external integration point, this can for example be the POS wanting to return a custom message why an article was not allowed to be added to a shopping trip.", "contact": {"email": "<EMAIL>"}, "version": "v1.0"}, "paths": {"/connect/token": {"post": {"tags": ["Token"], "summary": "Sign in.", "description": "Requests a new access token (and optional refresh token) using password or refresh_token grants.", "operationId": "RequestToken", "parameters": [{"name": "Accept-Language", "in": "header", "schema": {"type": "string"}}, {"name": "X-Correlation-ID", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/x-www-form-urlencoded": {"schema": {"type": "object", "properties": {"grant_type": {"type": "string", "description": "Gets or sets the grant type."}, "username": {"type": "string", "description": "Gets or sets the username."}, "password": {"type": "string", "description": "Gets or sets the password."}, "scope": {"type": "string", "description": "Gets or sets the scope."}, "refresh_token": {"type": "string", "description": "Gets or sets the refresh token."}, "client_id": {"type": "string", "description": "Gets or sets the client id."}, "client_secret": {"type": "string", "description": "Gets or sets the client secret."}}}, "encoding": {"grant_type": {"style": "form"}, "username": {"style": "form"}, "password": {"style": "form"}, "scope": {"style": "form"}, "refresh_token": {"style": "form"}, "client_id": {"style": "form"}, "client_secret": {"style": "form"}}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SignInResult"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OpenIddictResponse"}}}}, "500": {"description": "Server Error"}}}}, "/Sites/{siteNumber}/Payments/{cartId}": {"put": {"tags": ["ShoppingTrip"], "summary": "Performs payment for a basket id.", "description": "Performs payment for a basket id, and returns the current status. If the payment is asynchronous return status pending.", "operationId": "PutPayment", "parameters": [{"name": "siteNumber", "in": "path", "description": "The store number of the shopping trip.", "required": true, "schema": {"type": "string"}}, {"name": "cartId", "in": "path", "description": "The basket id.", "required": true, "schema": {"type": "string"}}, {"name": "X-Shopper-Identifier", "in": "header", "description": "The identifier of the shopper.", "schema": {"type": "string"}}, {"name": "Accept-Language", "in": "header", "schema": {"type": "string"}}, {"name": "X-Correlation-ID", "in": "header", "schema": {"type": "string"}}], "requestBody": {"description": "The payment request for a shopping trip.", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/PaymentRequestDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PaymentRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PaymentRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PaymentRequestDto"}}}, "required": true}, "responses": {"200": {"description": "Successful operation.", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PaymentStatusDto"}, "example": {"shoppingTripId": "123", "shopperPaymentId": "1552de31-004a-42e5-82fa-7d9daf3ac367", "status": 1, "message": "Mastercard Identity Check in progress.", "statusPayload": null}}, "application/json": {"schema": {"$ref": "#/components/schemas/PaymentStatusDto"}, "example": {"shoppingTripId": "123", "shopperPaymentId": "1552de31-004a-42e5-82fa-7d9daf3ac367", "status": 1, "message": "Mastercard Identity Check in progress.", "statusPayload": null}}, "text/json": {"schema": {"$ref": "#/components/schemas/PaymentStatusDto"}, "example": {"shoppingTripId": "123", "shopperPaymentId": "1552de31-004a-42e5-82fa-7d9daf3ac367", "status": 1, "message": "Mastercard Identity Check in progress.", "statusPayload": null}}}}, "400": {"description": "Bad Request - See the error response for details.", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}}}, "401": {"description": "Not Authorized."}, "403": {"description": "Forbidden - Ownership of the shopping trip has been transferred from external to retail owned device. See the error response for details.", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}}}, "404": {"description": "Not Found - See the error response for details.", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}}}, "500": {"description": "Internal Server Error - An unexpected problem was encountered or propagated from underlying dependencies, see the error response for more details.", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}}}}}, "get": {"tags": ["ShoppingTrip"], "summary": "Get the payment status for a basket id.", "description": "Get the payment status for the basket id. This can be used by a client if the payment is performed asynchronously to poll the current status of the payment.", "operationId": "GetPayment", "parameters": [{"name": "siteNumber", "in": "path", "description": "The store number of the shopping trip.", "required": true, "schema": {"type": "string"}}, {"name": "cartId", "in": "path", "description": "The basket id.", "required": true, "schema": {"type": "string"}}, {"name": "X-Shopper-Identifier", "in": "header", "description": "The identifier of the shopper.", "schema": {"type": "string"}}, {"name": "Accept-Language", "in": "header", "schema": {"type": "string"}}, {"name": "X-Correlation-ID", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "Successful operation.", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PaymentStatusDto"}, "example": {"shoppingTripId": "123", "shopperPaymentId": "1552de31-004a-42e5-82fa-7d9daf3ac367", "status": 1, "message": "Mastercard Identity Check in progress.", "statusPayload": null}}, "application/json": {"schema": {"$ref": "#/components/schemas/PaymentStatusDto"}, "example": {"shoppingTripId": "123", "shopperPaymentId": "1552de31-004a-42e5-82fa-7d9daf3ac367", "status": 1, "message": "Mastercard Identity Check in progress.", "statusPayload": null}}, "text/json": {"schema": {"$ref": "#/components/schemas/PaymentStatusDto"}, "example": {"shoppingTripId": "123", "shopperPaymentId": "1552de31-004a-42e5-82fa-7d9daf3ac367", "status": 1, "message": "Mastercard Identity Check in progress.", "statusPayload": null}}}}, "400": {"description": "Bad Request - See the error response for details.", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}}}, "401": {"description": "Not Authorized."}, "403": {"description": "Forbidden - Ownership of the shopping trip has been transferred from external to retail owned device. See the error response for details.", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}}}, "404": {"description": "Not Found - See the error response for details.", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}}}, "500": {"description": "Internal Server Error - An unexpected problem was encountered or propagated from underlying dependencies, see the error response for more details.", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}}}}}}, "/Sites/{siteNumber}/ShoppingTrips/{id}": {"get": {"tags": ["ShoppingTrip"], "summary": "Get a shopping trip.", "description": "Retrieves an active Shopping Trip from a specific Store that the authenticated user owns.", "operationId": "GetShoppingTrip", "parameters": [{"name": "siteNumber", "in": "path", "description": "", "required": true, "schema": {"type": "string"}}, {"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "string"}}, {"name": "X-Shopper-Identifier", "in": "header", "description": "", "schema": {"type": "string"}}, {"name": "Accept-Language", "in": "header", "schema": {"type": "string"}}, {"name": "X-Device-ID", "in": "header", "schema": {"type": "string"}}, {"name": "X-Correlation-ID", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ShoppingTripDto"}, "example": {"id": "123", "shopper": {"identifier": "145692837233", "firstName": "Easy", "lastName": "Shopper", "extensionData": null}, "cart": {"id": "1234", "paymentReference": null, "paymentAllowed": false, "items": [{"id": "1234-1", "type": 0, "article": {"barcode": {"data": "1234567890128", "symbology": 1}, "alternativeIdentities": null, "description": "EasyShop Treats", "price": 6.49, "productInformation": "EasyShop Treats contain no known allergens", "quantity": 1, "unitOfMeasure": 0, "fixedQuantity": false, "ageRequirement": 0, "timeRequirement": null, "securityTagged": false, "alternatives": [{"barcode": {"data": "1234567890128X", "symbology": 1}, "description": "EasyShop Treats MEGA PACK"}], "notification": null, "block": null, "extensionData": null}, "quantity": 2, "discounts": [{"id": "1", "description": "Treats for Less", "type": 6, "discountAmountPerQuantity": 1.49, "quantity": 2, "total": 2.98, "extensionData": null}], "vat": {"percent": 20, "total": 1.67}, "total": 10, "childItems": null}], "totalDiscount": 2.98, "total": 10, "vats": [{"percent": 20, "total": 1.67}]}, "endShoppingTripBarcode": "END_SHOPPING_TRIP"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ShoppingTripDto"}, "example": {"id": "123", "shopper": {"identifier": "145692837233", "firstName": "Easy", "lastName": "Shopper", "extensionData": null}, "cart": {"id": "1234", "paymentReference": null, "paymentAllowed": false, "items": [{"id": "1234-1", "type": 0, "article": {"barcode": {"data": "1234567890128", "symbology": 1}, "alternativeIdentities": null, "description": "EasyShop Treats", "price": 6.49, "productInformation": "EasyShop Treats contain no known allergens", "quantity": 1, "unitOfMeasure": 0, "fixedQuantity": false, "ageRequirement": 0, "timeRequirement": null, "securityTagged": false, "alternatives": [{"barcode": {"data": "1234567890128X", "symbology": 1}, "description": "EasyShop Treats MEGA PACK"}], "notification": null, "block": null, "extensionData": null}, "quantity": 2, "discounts": [{"id": "1", "description": "Treats for Less", "type": 6, "discountAmountPerQuantity": 1.49, "quantity": 2, "total": 2.98, "extensionData": null}], "vat": {"percent": 20, "total": 1.67}, "total": 10, "childItems": null}], "totalDiscount": 2.98, "total": 10, "vats": [{"percent": 20, "total": 1.67}]}, "endShoppingTripBarcode": "END_SHOPPING_TRIP"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ShoppingTripDto"}, "example": {"id": "123", "shopper": {"identifier": "145692837233", "firstName": "Easy", "lastName": "Shopper", "extensionData": null}, "cart": {"id": "1234", "paymentReference": null, "paymentAllowed": false, "items": [{"id": "1234-1", "type": 0, "article": {"barcode": {"data": "1234567890128", "symbology": 1}, "alternativeIdentities": null, "description": "EasyShop Treats", "price": 6.49, "productInformation": "EasyShop Treats contain no known allergens", "quantity": 1, "unitOfMeasure": 0, "fixedQuantity": false, "ageRequirement": 0, "timeRequirement": null, "securityTagged": false, "alternatives": [{"barcode": {"data": "1234567890128X", "symbology": 1}, "description": "EasyShop Treats MEGA PACK"}], "notification": null, "block": null, "extensionData": null}, "quantity": 2, "discounts": [{"id": "1", "description": "Treats for Less", "type": 6, "discountAmountPerQuantity": 1.49, "quantity": 2, "total": 2.98, "extensionData": null}], "vat": {"percent": 20, "total": 1.67}, "total": 10, "childItems": null}], "totalDiscount": 2.98, "total": 10, "vats": [{"percent": 20, "total": 1.67}]}, "endShoppingTripBarcode": "END_SHOPPING_TRIP"}}}}, "400": {"description": "Bad Request - See the error response for details.", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}}}, "403": {"description": "Forbidden - Ownership of the shopping trip has been transferred from external to retail owned device. See the error response for details.", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}}}, "404": {"description": "Not Found - See the error response for details.", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}}}, "500": {"description": "Internal Server Error - An unexpected problem was encountered or propagated from underlying dependencies, see the error response for more details.", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}}}}}, "delete": {"tags": ["ShoppingTrip"], "summary": "Cancel a shopping trip.", "description": "Cancels an active Shopping Trip in a specific Store that the authenticated user owns.", "operationId": "CancelShoppingTrip", "parameters": [{"name": "siteNumber", "in": "path", "description": "", "required": true, "schema": {"type": "string"}}, {"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "string"}}, {"name": "X-Shopper-Identifier", "in": "header", "description": "", "schema": {"type": "string"}}, {"name": "Accept-Language", "in": "header", "schema": {"type": "string"}}, {"name": "X-Device-ID", "in": "header", "schema": {"type": "string"}}, {"name": "X-Correlation-ID", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success - Shopping trip was cancelled."}, "400": {"description": "Bad Request - See the error response for details.", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}}}, "403": {"description": "Forbidden - Ownership of the shopping trip has been transferred from external to retail owned device. See the error response for details.", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}}}, "404": {"description": "Not Found - See the error response for details.", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}}}, "500": {"description": "Internal Server Error - An unexpected problem was encountered or propagated from underlying dependencies, see the error response for more details.", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}}}}}}, "/Sites/{siteNumber}/ShoppingTrips": {"post": {"tags": ["ShoppingTrip"], "summary": "Begin a shopping trip.", "description": "Begins a Shopping Trip for the authenticated user in a specific Store; if an active one already exists then the existing one will be returned.", "operationId": "BeginShoppingTrip", "parameters": [{"name": "siteNumber", "in": "path", "description": "", "required": true, "schema": {"type": "string"}}, {"name": "X-Shopper-Identifier", "in": "header", "description": "", "schema": {"type": "string"}}, {"name": "Accept-Language", "in": "header", "schema": {"type": "string"}}, {"name": "X-Device-ID", "in": "header", "schema": {"type": "string"}}, {"name": "X-Correlation-ID", "in": "header", "schema": {"type": "string"}}], "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/BeginShoppingTripDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BeginShoppingTripDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BeginShoppingTripDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BeginShoppingTripDto"}}}}, "responses": {"200": {"description": "Success - Shopping trip was started.", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ShoppingTripDto"}, "example": {"id": "123", "shopper": {"identifier": "145692837233", "firstName": "Easy", "lastName": "Shopper", "extensionData": null}, "cart": {"id": "123", "paymentReference": null, "paymentAllowed": false, "items": [], "totalDiscount": 0, "total": 0, "vats": []}, "endShoppingTripBarcode": "END_SHOPPING_TRIP"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ShoppingTripDto"}, "example": {"id": "123", "shopper": {"identifier": "145692837233", "firstName": "Easy", "lastName": "Shopper", "extensionData": null}, "cart": {"id": "123", "paymentReference": null, "paymentAllowed": false, "items": [], "totalDiscount": 0, "total": 0, "vats": []}, "endShoppingTripBarcode": "END_SHOPPING_TRIP"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ShoppingTripDto"}, "example": {"id": "123", "shopper": {"identifier": "145692837233", "firstName": "Easy", "lastName": "Shopper", "extensionData": null}, "cart": {"id": "123", "paymentReference": null, "paymentAllowed": false, "items": [], "totalDiscount": 0, "total": 0, "vats": []}, "endShoppingTripBarcode": "END_SHOPPING_TRIP"}}}}, "400": {"description": "Bad Request - See the error response for details.", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}}}, "409": {"description": "Conflict - The user already has an active shopping trip at the specified site. See the error response for details.", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}}}, "500": {"description": "Internal Server Error - An unexpected problem was encountered or propagated from underlying dependencies, see the error response for more details.", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}}}}}}, "/Sites/{siteNumber}/ShoppingTrips/{id}/End": {"put": {"tags": ["ShoppingTrip"], "summary": "End a shopping trip.", "description": "Ends an active Shopping Trip in a specific Store that the authenticated user owns.", "operationId": "EndShoppingTrip", "parameters": [{"name": "siteNumber", "in": "path", "description": "", "required": true, "schema": {"type": "string"}}, {"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "string"}}, {"name": "X-Shopper-Identifier", "in": "header", "description": "", "schema": {"type": "string"}}, {"name": "Accept-Language", "in": "header", "schema": {"type": "string"}}, {"name": "X-Device-ID", "in": "header", "schema": {"type": "string"}}, {"name": "X-Correlation-ID", "in": "header", "schema": {"type": "string"}}], "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/EndShoppingTripDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/EndShoppingTripDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/EndShoppingTripDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/EndShoppingTripDto"}}}, "required": true}, "responses": {"200": {"description": "Success - Shopping trip was ended.", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PutEndShoppingTripResponseDto"}, "example": {"id": "123", "shopper": {"identifier": "145692837233", "firstName": "Easy", "lastName": "Shopper", "extensionData": null}, "cart": {"id": "1234", "paymentReference": null, "paymentAllowed": false, "items": [{"id": "1234-1", "type": 0, "article": {"barcode": {"data": "1234567890128", "symbology": 1}, "alternativeIdentities": null, "description": "EasyShop Treats", "price": 6.49, "productInformation": "EasyShop Treats contain no known allergens", "quantity": 1, "unitOfMeasure": 0, "fixedQuantity": false, "ageRequirement": 0, "timeRequirement": null, "securityTagged": false, "alternatives": [{"barcode": {"data": "1234567890128X", "symbology": 1}, "description": "EasyShop Treats MEGA PACK"}], "notification": null, "block": null, "extensionData": null}, "quantity": 2, "discounts": [{"id": "1", "description": "Treats for Less", "type": 6, "discountAmountPerQuantity": 1.49, "quantity": 2, "total": 2.98, "extensionData": null}], "vat": {"percent": 20, "total": 1.67}, "total": 10, "childItems": null}], "totalDiscount": 2.98, "total": 10, "vats": [{"percent": 20, "total": 1.67}]}, "endShoppingTripBarcode": "END_SHOPPING_TRIP", "paymentAllowedStatus": 1, "paymentBlockedReason": null, "paymentReference": "PAYMENT_REFERENCE", "shoppingTripStatus": 2}}, "application/json": {"schema": {"$ref": "#/components/schemas/PutEndShoppingTripResponseDto"}, "example": {"id": "123", "shopper": {"identifier": "145692837233", "firstName": "Easy", "lastName": "Shopper", "extensionData": null}, "cart": {"id": "1234", "paymentReference": null, "paymentAllowed": false, "items": [{"id": "1234-1", "type": 0, "article": {"barcode": {"data": "1234567890128", "symbology": 1}, "alternativeIdentities": null, "description": "EasyShop Treats", "price": 6.49, "productInformation": "EasyShop Treats contain no known allergens", "quantity": 1, "unitOfMeasure": 0, "fixedQuantity": false, "ageRequirement": 0, "timeRequirement": null, "securityTagged": false, "alternatives": [{"barcode": {"data": "1234567890128X", "symbology": 1}, "description": "EasyShop Treats MEGA PACK"}], "notification": null, "block": null, "extensionData": null}, "quantity": 2, "discounts": [{"id": "1", "description": "Treats for Less", "type": 6, "discountAmountPerQuantity": 1.49, "quantity": 2, "total": 2.98, "extensionData": null}], "vat": {"percent": 20, "total": 1.67}, "total": 10, "childItems": null}], "totalDiscount": 2.98, "total": 10, "vats": [{"percent": 20, "total": 1.67}]}, "endShoppingTripBarcode": "END_SHOPPING_TRIP", "paymentAllowedStatus": 1, "paymentBlockedReason": null, "paymentReference": "PAYMENT_REFERENCE", "shoppingTripStatus": 2}}, "text/json": {"schema": {"$ref": "#/components/schemas/PutEndShoppingTripResponseDto"}, "example": {"id": "123", "shopper": {"identifier": "145692837233", "firstName": "Easy", "lastName": "Shopper", "extensionData": null}, "cart": {"id": "1234", "paymentReference": null, "paymentAllowed": false, "items": [{"id": "1234-1", "type": 0, "article": {"barcode": {"data": "1234567890128", "symbology": 1}, "alternativeIdentities": null, "description": "EasyShop Treats", "price": 6.49, "productInformation": "EasyShop Treats contain no known allergens", "quantity": 1, "unitOfMeasure": 0, "fixedQuantity": false, "ageRequirement": 0, "timeRequirement": null, "securityTagged": false, "alternatives": [{"barcode": {"data": "1234567890128X", "symbology": 1}, "description": "EasyShop Treats MEGA PACK"}], "notification": null, "block": null, "extensionData": null}, "quantity": 2, "discounts": [{"id": "1", "description": "Treats for Less", "type": 6, "discountAmountPerQuantity": 1.49, "quantity": 2, "total": 2.98, "extensionData": null}], "vat": {"percent": 20, "total": 1.67}, "total": 10, "childItems": null}], "totalDiscount": 2.98, "total": 10, "vats": [{"percent": 20, "total": 1.67}]}, "endShoppingTripBarcode": "END_SHOPPING_TRIP", "paymentAllowedStatus": 1, "paymentBlockedReason": null, "paymentReference": "PAYMENT_REFERENCE", "shoppingTripStatus": 2}}}}, "204": {"description": "No Content - Empty shopping trip was ended."}, "400": {"description": "Bad Request - See the error response for details.", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}}}, "403": {"description": "Forbidden - Ownership of the shopping trip has been transferred from external to retail owned device. See the error response for details.", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}}}, "404": {"description": "Not Found - See the error response for details.", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}}}, "500": {"description": "Internal Server Error - An unexpected problem was encountered or propagated from underlying dependencies, see the error response for more details.", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}}}}}, "get": {"tags": ["ShoppingTrip"], "summary": "Gets the end state of a shopping trip.", "description": "Retrieves state related to ending a Shopping Trip in a specific Store that the authenticated user owns.", "operationId": "GetEndShoppingTripState", "parameters": [{"name": "siteNumber", "in": "path", "description": "", "required": true, "schema": {"type": "string"}}, {"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "string"}}, {"name": "X-Shopper-Identifier", "in": "header", "description": "", "schema": {"type": "string"}}, {"name": "fields", "in": "query", "description": "", "schema": {"type": "string"}}, {"name": "Accept-Language", "in": "header", "schema": {"type": "string"}}, {"name": "X-Device-ID", "in": "header", "schema": {"type": "string"}}, {"name": "X-Correlation-ID", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/EndShoppingTripStateDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/EndShoppingTripStateDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/EndShoppingTripStateDto"}}}}, "400": {"description": "Bad Request - See the error response for details.", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}}}, "404": {"description": "Not Found - See the error response for details.", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}}}, "500": {"description": "Internal Server Error - An unexpected problem was encountered or propagated from underlying dependencies, see the error response for more details.", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}}}}}}, "/Sites/{siteNumber}/ShoppingTrips/{id}/Items": {"post": {"tags": ["ShoppingTrip"], "summary": "Add or Remove an item to the shopping trip.", "description": "Adds or Removes an item associated with the barcode from an active Shopping Trip in a specific Store that the authenticated user owns. A positive Quantity indicates an Add while a zero or negative Quantity indicates a Remove. Depending on what type of item it is, the result might be that a new item is added, an existing item is removed or an item has its quantity changed.", "operationId": "AddRemoveItem", "parameters": [{"name": "siteNumber", "in": "path", "description": "", "required": true, "schema": {"type": "string"}}, {"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "string"}}, {"name": "X-Shopper-Identifier", "in": "header", "description": "", "schema": {"type": "string"}}, {"name": "Accept-Language", "in": "header", "schema": {"type": "string"}}, {"name": "X-Device-ID", "in": "header", "schema": {"type": "string"}}, {"name": "X-Correlation-ID", "in": "header", "schema": {"type": "string"}}], "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/AddRemoveItemDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AddRemoveItemDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AddRemoveItemDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AddRemoveItemDto"}}}, "required": true}, "responses": {"200": {"description": "Success - The add or remove request was successful. An item was added, removed or had its quantity changed.", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CartDto"}, "example": {"id": "1234", "paymentReference": null, "paymentAllowed": false, "items": [{"id": "1234-1", "type": 0, "article": {"barcode": {"data": "1234567890128", "symbology": 1}, "alternativeIdentities": null, "description": "EasyShop Treats", "price": 6.49, "productInformation": "EasyShop Treats contain no known allergens", "quantity": 1, "unitOfMeasure": 0, "fixedQuantity": false, "ageRequirement": 0, "timeRequirement": null, "securityTagged": false, "alternatives": [{"barcode": {"data": "1234567890128X", "symbology": 1}, "description": "EasyShop Treats MEGA PACK"}], "notification": null, "block": null, "extensionData": null}, "quantity": 2, "discounts": [{"id": "1", "description": "Treats for Less", "type": 6, "discountAmountPerQuantity": 1.49, "quantity": 2, "total": 2.98, "extensionData": null}], "vat": {"percent": 20, "total": 1.67}, "total": 10, "childItems": null}], "totalDiscount": 2.98, "total": 10, "vats": [{"percent": 20, "total": 1.67}]}}, "application/json": {"schema": {"$ref": "#/components/schemas/CartDto"}, "example": {"id": "1234", "paymentReference": null, "paymentAllowed": false, "items": [{"id": "1234-1", "type": 0, "article": {"barcode": {"data": "1234567890128", "symbology": 1}, "alternativeIdentities": null, "description": "EasyShop Treats", "price": 6.49, "productInformation": "EasyShop Treats contain no known allergens", "quantity": 1, "unitOfMeasure": 0, "fixedQuantity": false, "ageRequirement": 0, "timeRequirement": null, "securityTagged": false, "alternatives": [{"barcode": {"data": "1234567890128X", "symbology": 1}, "description": "EasyShop Treats MEGA PACK"}], "notification": null, "block": null, "extensionData": null}, "quantity": 2, "discounts": [{"id": "1", "description": "Treats for Less", "type": 6, "discountAmountPerQuantity": 1.49, "quantity": 2, "total": 2.98, "extensionData": null}], "vat": {"percent": 20, "total": 1.67}, "total": 10, "childItems": null}], "totalDiscount": 2.98, "total": 10, "vats": [{"percent": 20, "total": 1.67}]}}, "text/json": {"schema": {"$ref": "#/components/schemas/CartDto"}, "example": {"id": "1234", "paymentReference": null, "paymentAllowed": false, "items": [{"id": "1234-1", "type": 0, "article": {"barcode": {"data": "1234567890128", "symbology": 1}, "alternativeIdentities": null, "description": "EasyShop Treats", "price": 6.49, "productInformation": "EasyShop Treats contain no known allergens", "quantity": 1, "unitOfMeasure": 0, "fixedQuantity": false, "ageRequirement": 0, "timeRequirement": null, "securityTagged": false, "alternatives": [{"barcode": {"data": "1234567890128X", "symbology": 1}, "description": "EasyShop Treats MEGA PACK"}], "notification": null, "block": null, "extensionData": null}, "quantity": 2, "discounts": [{"id": "1", "description": "Treats for Less", "type": 6, "discountAmountPerQuantity": 1.49, "quantity": 2, "total": 2.98, "extensionData": null}], "vat": {"percent": 20, "total": 1.67}, "total": 10, "childItems": null}], "totalDiscount": 2.98, "total": 10, "vats": [{"percent": 20, "total": 1.67}]}}}}, "400": {"description": "Bad Request - See the error response for details.", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}}}, "403": {"description": "Forbidden - Ownership of the shopping trip has been transferred from external to retail owned device. See the error response for details.", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}}}, "404": {"description": "Not Found - See the error response for details.", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}}}, "500": {"description": "Internal Server Error - An unexpected problem was encountered or propagated from underlying dependencies, see the error response for more details.", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}}}}}}, "/Sites/{siteNumber}/ShoppingTrips/{id}/Items/{itemId}": {"delete": {"tags": ["ShoppingTrip"], "summary": "Remove a cart item from the shopping trip.", "description": "Removes a Cart Item from an active Shopping Trip in a specific Store that the authenticated user owns.", "operationId": "RemoveTripItem", "parameters": [{"name": "siteNumber", "in": "path", "description": "", "required": true, "schema": {"type": "string"}}, {"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "string"}}, {"name": "itemId", "in": "path", "description": "", "required": true, "schema": {"type": "string"}}, {"name": "X-Shopper-Identifier", "in": "header", "description": "", "schema": {"type": "string"}}, {"name": "Accept-Language", "in": "header", "schema": {"type": "string"}}, {"name": "X-Device-ID", "in": "header", "schema": {"type": "string"}}, {"name": "X-Correlation-ID", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success - Cart item was removed.", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CartDto"}, "example": {"id": "1234", "paymentReference": null, "paymentAllowed": false, "items": [{"id": "1234-1", "type": 0, "article": {"barcode": {"data": "1234567890128", "symbology": 1}, "alternativeIdentities": null, "description": "EasyShop Treats", "price": 6.49, "productInformation": "EasyShop Treats contain no known allergens", "quantity": 1, "unitOfMeasure": 0, "fixedQuantity": false, "ageRequirement": 0, "timeRequirement": null, "securityTagged": false, "alternatives": [{"barcode": {"data": "1234567890128X", "symbology": 1}, "description": "EasyShop Treats MEGA PACK"}], "notification": null, "block": null, "extensionData": null}, "quantity": 2, "discounts": [{"id": "1", "description": "Treats for Less", "type": 6, "discountAmountPerQuantity": 1.49, "quantity": 2, "total": 2.98, "extensionData": null}], "vat": {"percent": 20, "total": 1.67}, "total": 10, "childItems": null}], "totalDiscount": 2.98, "total": 10, "vats": [{"percent": 20, "total": 1.67}]}}, "application/json": {"schema": {"$ref": "#/components/schemas/CartDto"}, "example": {"id": "1234", "paymentReference": null, "paymentAllowed": false, "items": [{"id": "1234-1", "type": 0, "article": {"barcode": {"data": "1234567890128", "symbology": 1}, "alternativeIdentities": null, "description": "EasyShop Treats", "price": 6.49, "productInformation": "EasyShop Treats contain no known allergens", "quantity": 1, "unitOfMeasure": 0, "fixedQuantity": false, "ageRequirement": 0, "timeRequirement": null, "securityTagged": false, "alternatives": [{"barcode": {"data": "1234567890128X", "symbology": 1}, "description": "EasyShop Treats MEGA PACK"}], "notification": null, "block": null, "extensionData": null}, "quantity": 2, "discounts": [{"id": "1", "description": "Treats for Less", "type": 6, "discountAmountPerQuantity": 1.49, "quantity": 2, "total": 2.98, "extensionData": null}], "vat": {"percent": 20, "total": 1.67}, "total": 10, "childItems": null}], "totalDiscount": 2.98, "total": 10, "vats": [{"percent": 20, "total": 1.67}]}}, "text/json": {"schema": {"$ref": "#/components/schemas/CartDto"}, "example": {"id": "1234", "paymentReference": null, "paymentAllowed": false, "items": [{"id": "1234-1", "type": 0, "article": {"barcode": {"data": "1234567890128", "symbology": 1}, "alternativeIdentities": null, "description": "EasyShop Treats", "price": 6.49, "productInformation": "EasyShop Treats contain no known allergens", "quantity": 1, "unitOfMeasure": 0, "fixedQuantity": false, "ageRequirement": 0, "timeRequirement": null, "securityTagged": false, "alternatives": [{"barcode": {"data": "1234567890128X", "symbology": 1}, "description": "EasyShop Treats MEGA PACK"}], "notification": null, "block": null, "extensionData": null}, "quantity": 2, "discounts": [{"id": "1", "description": "Treats for Less", "type": 6, "discountAmountPerQuantity": 1.49, "quantity": 2, "total": 2.98, "extensionData": null}], "vat": {"percent": 20, "total": 1.67}, "total": 10, "childItems": null}], "totalDiscount": 2.98, "total": 10, "vats": [{"percent": 20, "total": 1.67}]}}}}, "400": {"description": "Bad Request - See the error response for details.", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}}}, "403": {"description": "Forbidden - Ownership of the shopping trip has been transferred from external to retail owned device. See the error response for details.", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}}}, "404": {"description": "Not Found - See the error response for details.", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}}}, "500": {"description": "Internal Server Error - An unexpected problem was encountered or propagated from underlying dependencies, see the error response for more details.", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}}}}}, "put": {"tags": ["ShoppingTrip"], "summary": "Change the quantity of a cart item.", "description": "Changes the Quantity of a Cart Item on an active Shopping Trip in a specific Store that the authenticated user owns.", "operationId": "ChangeTripItemQuantity", "parameters": [{"name": "siteNumber", "in": "path", "description": "", "required": true, "schema": {"type": "string"}}, {"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "string"}}, {"name": "itemId", "in": "path", "description": "", "required": true, "schema": {"type": "string"}}, {"name": "X-Shopper-Identifier", "in": "header", "description": "", "schema": {"type": "string"}}, {"name": "Accept-Language", "in": "header", "schema": {"type": "string"}}, {"name": "X-Device-ID", "in": "header", "schema": {"type": "string"}}, {"name": "X-Correlation-ID", "in": "header", "schema": {"type": "string"}}], "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/ChangeTripItemQuantityDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ChangeTripItemQuantityDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ChangeTripItemQuantityDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ChangeTripItemQuantityDto"}}}, "required": true}, "responses": {"200": {"description": "Success - The cart item quantity was updated.", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CartDto"}, "example": {"id": "1234", "paymentReference": null, "paymentAllowed": false, "items": [{"id": "1234-1", "type": 0, "article": {"barcode": {"data": "1234567890128", "symbology": 1}, "alternativeIdentities": null, "description": "EasyShop Treats", "price": 6.49, "productInformation": "EasyShop Treats contain no known allergens", "quantity": 1, "unitOfMeasure": 0, "fixedQuantity": false, "ageRequirement": 0, "timeRequirement": null, "securityTagged": false, "alternatives": [{"barcode": {"data": "1234567890128X", "symbology": 1}, "description": "EasyShop Treats MEGA PACK"}], "notification": null, "block": null, "extensionData": null}, "quantity": 2, "discounts": [{"id": "1", "description": "Treats for Less", "type": 6, "discountAmountPerQuantity": 1.49, "quantity": 2, "total": 2.98, "extensionData": null}], "vat": {"percent": 20, "total": 1.67}, "total": 10, "childItems": null}], "totalDiscount": 2.98, "total": 10, "vats": [{"percent": 20, "total": 1.67}]}}, "application/json": {"schema": {"$ref": "#/components/schemas/CartDto"}, "example": {"id": "1234", "paymentReference": null, "paymentAllowed": false, "items": [{"id": "1234-1", "type": 0, "article": {"barcode": {"data": "1234567890128", "symbology": 1}, "alternativeIdentities": null, "description": "EasyShop Treats", "price": 6.49, "productInformation": "EasyShop Treats contain no known allergens", "quantity": 1, "unitOfMeasure": 0, "fixedQuantity": false, "ageRequirement": 0, "timeRequirement": null, "securityTagged": false, "alternatives": [{"barcode": {"data": "1234567890128X", "symbology": 1}, "description": "EasyShop Treats MEGA PACK"}], "notification": null, "block": null, "extensionData": null}, "quantity": 2, "discounts": [{"id": "1", "description": "Treats for Less", "type": 6, "discountAmountPerQuantity": 1.49, "quantity": 2, "total": 2.98, "extensionData": null}], "vat": {"percent": 20, "total": 1.67}, "total": 10, "childItems": null}], "totalDiscount": 2.98, "total": 10, "vats": [{"percent": 20, "total": 1.67}]}}, "text/json": {"schema": {"$ref": "#/components/schemas/CartDto"}, "example": {"id": "1234", "paymentReference": null, "paymentAllowed": false, "items": [{"id": "1234-1", "type": 0, "article": {"barcode": {"data": "1234567890128", "symbology": 1}, "alternativeIdentities": null, "description": "EasyShop Treats", "price": 6.49, "productInformation": "EasyShop Treats contain no known allergens", "quantity": 1, "unitOfMeasure": 0, "fixedQuantity": false, "ageRequirement": 0, "timeRequirement": null, "securityTagged": false, "alternatives": [{"barcode": {"data": "1234567890128X", "symbology": 1}, "description": "EasyShop Treats MEGA PACK"}], "notification": null, "block": null, "extensionData": null}, "quantity": 2, "discounts": [{"id": "1", "description": "Treats for Less", "type": 6, "discountAmountPerQuantity": 1.49, "quantity": 2, "total": 2.98, "extensionData": null}], "vat": {"percent": 20, "total": 1.67}, "total": 10, "childItems": null}], "totalDiscount": 2.98, "total": 10, "vats": [{"percent": 20, "total": 1.67}]}}}}, "400": {"description": "Bad Request - See the error response for details.", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}}}, "403": {"description": "Forbidden - Ownership of the shopping trip has been transferred from external to retail owned device. See the error response for details.", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}}}, "404": {"description": "Not Found - See the error response for details.", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}}}, "500": {"description": "Internal Server Error - An unexpected problem was encountered or propagated from underlying dependencies, see the error response for more details.", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}}}}}}, "/Sites/{siteNumber}/ShoppingTrips/{id}/Final": {"get": {"tags": ["ShoppingTrip"], "summary": "Get a final cart with all discounts and final sale prices.", "description": "Gets the Final Cart including all discounts and final sale prices. This is not required if the Shopping Trip's Discounts, and Sale Price, is continuously calculated on Add, Remove and Change.", "operationId": "GetFinalCart", "parameters": [{"name": "siteNumber", "in": "path", "description": "", "required": true, "schema": {"type": "string"}}, {"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "string"}}, {"name": "X-Shopper-Identifier", "in": "header", "description": "", "schema": {"type": "string"}}, {"name": "Accept-Language", "in": "header", "schema": {"type": "string"}}, {"name": "X-Device-ID", "in": "header", "schema": {"type": "string"}}, {"name": "X-Correlation-ID", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success - The final cart with all discounts and final sale prices was returned.", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CartDto"}, "example": {"id": "1234", "paymentReference": null, "paymentAllowed": false, "items": [{"id": "1234-1", "type": 0, "article": {"barcode": {"data": "1234567890128", "symbology": 1}, "alternativeIdentities": null, "description": "EasyShop Treats", "price": 6.49, "productInformation": "EasyShop Treats contain no known allergens", "quantity": 1, "unitOfMeasure": 0, "fixedQuantity": false, "ageRequirement": 0, "timeRequirement": null, "securityTagged": false, "alternatives": [{"barcode": {"data": "1234567890128X", "symbology": 1}, "description": "EasyShop Treats MEGA PACK"}], "notification": null, "block": null, "extensionData": null}, "quantity": 2, "discounts": [{"id": "1", "description": "Treats for Less", "type": 6, "discountAmountPerQuantity": 1.49, "quantity": 2, "total": 2.98, "extensionData": null}], "vat": {"percent": 20, "total": 1.67}, "total": 10, "childItems": null}], "totalDiscount": 2.98, "total": 10, "vats": [{"percent": 20, "total": 1.67}]}}, "application/json": {"schema": {"$ref": "#/components/schemas/CartDto"}, "example": {"id": "1234", "paymentReference": null, "paymentAllowed": false, "items": [{"id": "1234-1", "type": 0, "article": {"barcode": {"data": "1234567890128", "symbology": 1}, "alternativeIdentities": null, "description": "EasyShop Treats", "price": 6.49, "productInformation": "EasyShop Treats contain no known allergens", "quantity": 1, "unitOfMeasure": 0, "fixedQuantity": false, "ageRequirement": 0, "timeRequirement": null, "securityTagged": false, "alternatives": [{"barcode": {"data": "1234567890128X", "symbology": 1}, "description": "EasyShop Treats MEGA PACK"}], "notification": null, "block": null, "extensionData": null}, "quantity": 2, "discounts": [{"id": "1", "description": "Treats for Less", "type": 6, "discountAmountPerQuantity": 1.49, "quantity": 2, "total": 2.98, "extensionData": null}], "vat": {"percent": 20, "total": 1.67}, "total": 10, "childItems": null}], "totalDiscount": 2.98, "total": 10, "vats": [{"percent": 20, "total": 1.67}]}}, "text/json": {"schema": {"$ref": "#/components/schemas/CartDto"}, "example": {"id": "1234", "paymentReference": null, "paymentAllowed": false, "items": [{"id": "1234-1", "type": 0, "article": {"barcode": {"data": "1234567890128", "symbology": 1}, "alternativeIdentities": null, "description": "EasyShop Treats", "price": 6.49, "productInformation": "EasyShop Treats contain no known allergens", "quantity": 1, "unitOfMeasure": 0, "fixedQuantity": false, "ageRequirement": 0, "timeRequirement": null, "securityTagged": false, "alternatives": [{"barcode": {"data": "1234567890128X", "symbology": 1}, "description": "EasyShop Treats MEGA PACK"}], "notification": null, "block": null, "extensionData": null}, "quantity": 2, "discounts": [{"id": "1", "description": "Treats for Less", "type": 6, "discountAmountPerQuantity": 1.49, "quantity": 2, "total": 2.98, "extensionData": null}], "vat": {"percent": 20, "total": 1.67}, "total": 10, "childItems": null}], "totalDiscount": 2.98, "total": 10, "vats": [{"percent": 20, "total": 1.67}]}}}}, "400": {"description": "Bad Request - See the error response for details.", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}}}, "403": {"description": "Forbidden - Ownership of the shopping trip has been transferred from external to retail owned device. See the error response for details.", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}}}, "404": {"description": "Not Found - See the error response for details.", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}}}, "500": {"description": "Internal Server Error - An unexpected problem was encountered or propagated from underlying dependencies, see the error response for more details.", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorDto"}}}}}}}, "/health": {"get": {"tags": ["Health"], "summary": "Get a health report.", "description": "Retrieves a health report from available health checks.", "operationId": "GetHealth", "responses": {"200": {"description": null, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HealthReport"}}}}, "503": {"description": null, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HealthReport"}}}}}}}}, "components": {"schemas": {"AddRemoveItemDto": {"required": ["barcode", "quantity"], "type": "object", "properties": {"barcode": {"$ref": "#/components/schemas/BarcodeDto"}, "quantity": {"type": "number", "description": "Gets or sets a quantity value that indicates if it's a Add or Remove request. (If the value is greater than 0 then it will be treated as an\r\nAdd request, if it's less than or equal to 0 it will be treated\r\nas a Remove request.)", "format": "double", "example": 1}}, "additionalProperties": false, "description": "Represents an Add or Remove Item request."}, "ArticleAlternativeDto": {"type": "object", "properties": {"barcode": {"$ref": "#/components/schemas/BarcodeDto"}, "description": {"type": "string", "description": "Gets or sets the description.", "nullable": true}}, "additionalProperties": false}, "ArticleDto": {"required": ["ageRequirement", "barcode", "description", "fixedQuantity", "price", "quantity", "securityTagged", "unitOfMeasure"], "type": "object", "properties": {"barcode": {"$ref": "#/components/schemas/BarcodeDto"}, "alternativeIdentities": {"type": "array", "items": {"type": "string"}, "description": "Gets or sets alternative identities.", "nullable": true}, "description": {"type": "string", "description": "Gets or sets the description."}, "price": {"type": "number", "description": "Gets or sets the unit price.", "format": "double"}, "productInformation": {"type": "string", "description": "Gets or sets the product information.", "nullable": true}, "quantity": {"type": "number", "description": "Gets or sets the quantity associated with the barcode.", "format": "double"}, "unitOfMeasure": {"$ref": "#/components/schemas/UnitOfMeasureDto"}, "fixedQuantity": {"type": "boolean", "description": "Gets or sets the fixed quantity flag indicating if the article and barcode combination allows changing of the quantity or not. (This flag is true in most of the cases where the barcode contains quantity information, an example of such a barcode is a weight barcode.)"}, "ageRequirement": {"type": "integer", "description": "Gets or sets the age requirement.", "format": "int32"}, "timeRequirement": {"$ref": "#/components/schemas/TimeRequirementDto"}, "securityTagged": {"type": "boolean", "description": "Gets or sets the security tagged flag indicating if the article is tagged with any form of security technology such as RFID."}, "alternatives": {"type": "array", "items": {"$ref": "#/components/schemas/ArticleAlternativeDto"}, "description": "Gets or sets article alternatives.", "nullable": true}, "notification": {"$ref": "#/components/schemas/NotificationDto"}, "block": {"$ref": "#/components/schemas/BlockDto"}, "extensionData": {"$ref": "#/components/schemas/ExtensionDataDto"}}, "additionalProperties": false, "description": "Represents an article in combination with a barcode."}, "AuthenticationProperties": {"type": "object", "properties": {"items": {"type": "object", "additionalProperties": {"type": "string"}, "nullable": true}, "parameters": {"type": "object", "additionalProperties": {}, "nullable": true}, "isPersistent": {"type": "boolean"}, "redirectUri": {"type": "string", "nullable": true}, "issuedUtc": {"type": "string", "format": "date-time", "nullable": true}, "expiresUtc": {"type": "string", "format": "date-time", "nullable": true}, "allowRefresh": {"type": "boolean", "nullable": true}}, "additionalProperties": false}, "BarcodeDto": {"required": ["data", "symbology"], "type": "object", "properties": {"data": {"type": "string", "description": "Gets or sets the data contained in the barcode.", "example": "1234567890123"}, "symbology": {"$ref": "#/components/schemas/BarcodeSymbologyDto"}}, "additionalProperties": false, "description": "Represents a barcode."}, "BarcodeSymbologyDto": {"enum": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34], "type": "integer", "description": "Identifies the symbology of a barcode.\r\n\r\n0 = Unknown\r\n\r\n1 = EAN13\r\n\r\n2 = EAN8\r\n\r\n3 = UPCA\r\n\r\n4 = UPCE\r\n\r\n5 = Code3Of9\r\n\r\n6 = Code128\r\n\r\n7 = CodeInterleaved2Of5\r\n\r\n8 = Codabar\r\n\r\n9 = UCCEAN128\r\n\r\n10 = Code93\r\n\r\n11 = EANEXT5\r\n\r\n12 = EANEXT2\r\n\r\n13 = MSI\r\n\r\n14 = Code11\r\n\r\n15 = CodeStandard2Of5\r\n\r\n16 = GS1Databar\r\n\r\n17 = GS1DatabarLimited\r\n\r\n18 = GS1DatabarExpanded\r\n\r\n19 = PatchCode\r\n\r\n20 = PostNet\r\n\r\n21 = Planet\r\n\r\n22 = AustralianPost4State\r\n\r\n23 = RoyalMail4State\r\n\r\n24 = USPS4State\r\n\r\n25 = GS1DatabarStacked\r\n\r\n26 = GS1DatabarExpandedStacked\r\n\r\n27 = PDF417\r\n\r\n28 = MicroPDF417\r\n\r\n29 = Datamatrix\r\n\r\n30 = QR\r\n\r\n31 = Aztec\r\n\r\n32 = Maxi\r\n\r\n33 = MicroQR\r\n\r\n34 = PharmaCode", "format": "int32", "x-enumNames": ["Unknown", "EAN13", "EAN8", "UPCA", "UPCE", "Code3Of9", "Code128", "CodeInterleaved2Of5", "Codabar", "UCCEAN128", "Code93", "EANEXT5", "EANEXT2", "MSI", "Code11", "CodeStandard2Of5", "GS1Databar", "GS1DatabarLimited", "GS1DatabarExpanded", "PatchCode", "PostNet", "Planet", "AustralianPost4State", "RoyalMail4State", "USPS4State", "GS1DatabarStacked", "GS1DatabarExpandedStacked", "PDF417", "MicroPDF417", "Datamatrix", "QR", "Aztec", "<PERSON><PERSON>", "MicroQR", "PharmaCode"]}, "BeginShoppingTripDto": {"type": "object", "properties": {"deviceInfo": {"$ref": "#/components/schemas/DeviceInfoDto"}}, "additionalProperties": false}, "BlockDto": {"type": "object", "properties": {"messages": {"type": "array", "items": {"$ref": "#/components/schemas/LocalizedMessageDto"}, "description": "List of localized messages related to the block.", "nullable": true}}, "additionalProperties": false, "description": "Represents that a block exists on an article."}, "CartDto": {"required": ["id", "items", "paymentAllowed", "total", "totalDiscount", "vats"], "type": "object", "properties": {"id": {"type": "string", "description": "Gets or sets the id."}, "paymentReference": {"type": "string", "description": "Gets or sets the payment reference.", "nullable": true}, "paymentAllowed": {"type": "boolean", "description": "Gets or sets the payment allowed flag."}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/CartItemDto"}, "description": "Gets or sets the cart items."}, "totalDiscount": {"type": "number", "description": "Gets or sets the total discount.", "format": "double"}, "total": {"type": "number", "description": "Gets or sets the sale total.", "format": "double"}, "vats": {"type": "array", "items": {"$ref": "#/components/schemas/VatDto"}, "description": "Gets or sets the VATs."}}, "additionalProperties": false, "description": "Represents a cart."}, "CartItemDto": {"required": ["article", "childItems", "discounts", "id", "quantity", "total", "type", "vat"], "type": "object", "properties": {"id": {"type": "string", "description": "Gets or sets the id."}, "type": {"$ref": "#/components/schemas/CartItemTypeDto"}, "article": {"$ref": "#/components/schemas/ArticleDto"}, "quantity": {"type": "number", "description": "Gets or sets the sale quantity. (The unit of measure of the quantity is found on the article.)", "format": "double"}, "discounts": {"type": "array", "items": {"$ref": "#/components/schemas/DiscountDto"}, "description": "Gets or sets the discounts."}, "vat": {"$ref": "#/components/schemas/VatDto"}, "total": {"type": "number", "description": "Gets or sets the sale total.", "format": "double"}, "childItems": {"type": "array", "items": {"$ref": "#/components/schemas/CartItemDto"}, "description": "Gets or sets the child items. (An example use of child items is for deposits.)"}}, "additionalProperties": false, "description": "Represents a cart item."}, "CartItemTypeDto": {"enum": [0, 1, 2, 3], "type": "integer", "description": "Identifies the type of cart item.\r\n\r\n0 = Normal\r\n\r\n1 = Discount\r\n\r\n2 = DiscountLoyaltyPoints\r\n\r\n3 = DiscountShopperPoints", "format": "int32", "x-enumNames": ["Normal", "Discount", "DiscountLoyaltyPoints", "DiscountShopperPoints"]}, "ChangeTripItemQuantityDto": {"required": ["quantity"], "type": "object", "properties": {"quantity": {"type": "number", "description": "Gets or sets the quantity.", "format": "double", "example": 2}}, "additionalProperties": false, "description": "Represents a Change Trip Item Quantity request."}, "Claim": {"type": "object", "properties": {"issuer": {"type": "string", "nullable": true}, "originalIssuer": {"type": "string", "nullable": true}, "properties": {"type": "object", "additionalProperties": {"type": "string"}, "nullable": true, "readOnly": true}, "subject": {"$ref": "#/components/schemas/ClaimsIdentity"}, "type": {"type": "string", "nullable": true}, "value": {"type": "string", "nullable": true}, "valueType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ClaimsIdentity": {"type": "object", "properties": {"authenticationType": {"type": "string", "nullable": true}, "isAuthenticated": {"type": "boolean", "readOnly": true}, "actor": {"$ref": "#/components/schemas/ClaimsIdentity"}, "bootstrapContext": {"nullable": true}, "claims": {"type": "array", "items": {"$ref": "#/components/schemas/Claim"}, "nullable": true}, "label": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true, "readOnly": true}, "nameClaimType": {"type": "string", "nullable": true, "readOnly": true}, "roleClaimType": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "ClaimsPrincipal": {"type": "object", "properties": {"claims": {"type": "array", "items": {"$ref": "#/components/schemas/Claim"}, "nullable": true, "readOnly": true}, "identities": {"type": "array", "items": {"$ref": "#/components/schemas/ClaimsIdentity"}, "nullable": true}, "identity": {"$ref": "#/components/schemas/IIdentity"}}, "additionalProperties": false}, "DeviceInfoDto": {"type": "object", "properties": {"id": {"type": "string", "description": "Gets or sets an identifier for the device. (This property is optional. The request header {X-Device-ID} can be used instead.\r\n<b>WARNING:</b> Please be aware that using this property or the request header will require that all\r\nsubsequent requests contain {X-Device-ID} with a matching value, otherwise the requests will fail.)", "nullable": true}, "modelName": {"type": "string", "description": "Gets or sets the device model name. (This property is optional.)", "nullable": true}, "operatingSystem": {"type": "string", "description": "Gets or sets the operating system running on the device. (This property is optional.)", "nullable": true}, "appVersion": {"type": "string", "description": "Gets or sets the version of the app running on the device. (This property is optional.)", "nullable": true}}, "additionalProperties": false, "description": "Represents information about a device."}, "DiscountDto": {"required": ["description", "discountAmountPerQuantity", "id", "quantity", "total", "type"], "type": "object", "properties": {"id": {"type": "string", "description": "Gets or sets the id."}, "description": {"type": "string", "description": "Gets or sets the description."}, "type": {"$ref": "#/components/schemas/DiscountTypeDto"}, "discountAmountPerQuantity": {"type": "number", "description": "Gets or sets the discount amount per quantity.", "format": "double"}, "quantity": {"type": "number", "description": "Gets or sets the quantity.", "format": "double"}, "total": {"type": "number", "description": "Gets or sets the total.", "format": "double"}, "extensionData": {"$ref": "#/components/schemas/ExtensionDataDto"}}, "additionalProperties": false, "description": "Represents a discount."}, "DiscountTypeDto": {"enum": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9], "type": "integer", "description": "Identifies the type of discount.\r\n\r\n0 = Line\r\n\r\n1 = Subtotal\r\n\r\n2 = Group\r\n\r\n3 = CustomerGroup\r\n\r\n4 = ArticleBundle\r\n\r\n5 = Model\r\n\r\n6 = MemberPrice\r\n\r\n7 = CampaignPrice\r\n\r\n8 = LoyaltyPoints\r\n\r\n9 = ShopperPoints", "format": "int32", "x-enumNames": ["Line", "Subtotal", "Group", "CustomerGroup", "ArticleBundle", "Model", "MemberPrice", "CampaignPrice", "LoyaltyPoints", "ShopperPoints"]}, "EndShoppingTripDto": {"type": "object", "properties": {"missingItems": {"type": "boolean", "description": "Gets or sets missing items flag.", "nullable": true, "example": false}, "barcode": {"$ref": "#/components/schemas/BarcodeDto"}, "externalEndOfTrip": {"type": "boolean", "description": "Gets or sets the external end of trip flag.", "nullable": true}}, "additionalProperties": false, "description": "Represents an End Shopping Trip request."}, "EndShoppingTripStateDto": {"required": ["paymentAllowedStatus", "shoppingTripStatus"], "type": "object", "properties": {"id": {"type": "string", "description": "Gets or sets the id.", "nullable": true}, "paymentAllowedStatus": {"$ref": "#/components/schemas/ShoppingTripPaymentAllowedStatusDto"}, "paymentBlockedReason": {"$ref": "#/components/schemas/ShoppingTripPaymentBlockedReasonDto"}, "paymentReference": {"type": "string", "description": "Gets or sets the payment reference.", "nullable": true}, "shoppingTripStatus": {"$ref": "#/components/schemas/ShoppingTripStatusDto"}, "cart": {"$ref": "#/components/schemas/CartDto"}}, "additionalProperties": false, "description": "Represents the state related to ending a shopping trip."}, "ErrorDto": {"required": ["code", "internalMessage", "message", "type"], "type": "object", "properties": {"code": {"type": "string", "description": "Gets or sets the code.", "example": "400"}, "internalMessage": {"type": "string", "description": "Gets or sets the internal message.", "example": "An internal message."}, "message": {"type": "string", "description": "Gets or sets the message.", "example": "A message."}, "type": {"type": "string", "description": "Gets or sets the type.", "example": "shopping-trip-not-found"}}, "additionalProperties": false, "description": "Represents an error."}, "ExtensionDataDto": {"type": "object", "additionalProperties": {}}, "HealthReport": {"type": "object", "properties": {"entries": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/HealthReportEntry"}, "nullable": true}, "status": {"$ref": "#/components/schemas/HealthStatus"}, "totalDuration": {"type": "string", "format": "date-span"}}, "additionalProperties": false}, "HealthReportEntry": {"type": "object", "properties": {"data": {"type": "object", "additionalProperties": {}, "nullable": true}, "description": {"type": "string", "nullable": true}, "duration": {"type": "string", "format": "date-span"}, "exception": {"nullable": true}, "status": {"$ref": "#/components/schemas/HealthStatus"}, "tags": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "HealthStatus": {"enum": [0, 1, 2], "type": "integer", "description": "\r\n\r\n0 = Unhealthy\r\n\r\n1 = Degraded\r\n\r\n2 = Healthy", "format": "int32", "x-enumNames": ["Unhealthy", "Degraded", "Healthy"]}, "IIdentity": {"type": "object", "properties": {"authenticationType": {"type": "string", "nullable": true, "readOnly": true}, "isAuthenticated": {"type": "boolean", "readOnly": true}, "name": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "LocalizedMessageDto": {"required": ["message"], "type": "object", "properties": {"message": {"type": "string", "description": "Gets or sets the message."}, "languageCode": {"type": "string", "description": "Gets or sets the language code. (IOS 639-1 language code. The property can be null or empty, indicating the default fallback message.)", "nullable": true}}, "additionalProperties": false, "description": "Represents a localized message."}, "NotificationDto": {"type": "object", "properties": {"messages": {"type": "array", "items": {"$ref": "#/components/schemas/LocalizedMessageDto"}, "description": "List of localized messages related to this notification.", "nullable": true}, "showBehavior": {"$ref": "#/components/schemas/NotificationShowBehaviorDto"}}, "additionalProperties": false, "description": "The class `NotificationDto`."}, "NotificationShowBehaviorDto": {"enum": [0, 1, 2, 3], "type": "integer", "description": "Identifies the show behavior of the notification.\r\n\r\n0 = No\r\n\r\n1 = Always\r\n\r\n2 = NewLine\r\n\r\n3 = NewItem", "format": "int32", "x-enumNames": ["No", "Always", "NewLine", "NewItem"]}, "OpenIddictResponse": {"type": "object", "properties": {"accessToken": {"type": "string", "nullable": true}, "code": {"type": "string", "nullable": true}, "deviceCode": {"type": "string", "nullable": true}, "error": {"type": "string", "nullable": true}, "errorDescription": {"type": "string", "nullable": true}, "errorUri": {"type": "string", "nullable": true}, "expiresIn": {"type": "integer", "format": "int64", "nullable": true}, "idToken": {"type": "string", "nullable": true}, "refreshToken": {"type": "string", "nullable": true}, "scope": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "tokenType": {"type": "string", "nullable": true}, "userCode": {"type": "string", "nullable": true}, "count": {"type": "integer", "format": "int32", "readOnly": true}}, "additionalProperties": false}, "PaymentExtensionDataDto": {"type": "object", "additionalProperties": {}}, "PaymentRequestDto": {"required": ["shopperPaymentId"], "type": "object", "properties": {"shopperPaymentId": {"type": "string", "description": "The id of the Shopper used to identify the Shopper in the payment system.", "example": "1552de31-004a-42e5-82fa-7d9daf3ac367"}, "shopperEmail": {"type": "string", "description": "The email address of the Shopper.", "nullable": true, "example": "<EMAIL>"}, "paymentPayload": {"$ref": "#/components/schemas/PaymentExtensionDataDto"}}, "additionalProperties": false, "description": "A payment request."}, "PaymentStatusDto": {"required": ["shoppingTripId", "status"], "type": "object", "properties": {"shoppingTripId": {"type": "string", "description": "The Id of the shopping trip.", "example": "123abc"}, "shopperPaymentId": {"type": "string", "description": "The id of the Shopper used to identify the Shopper in the payment system.", "nullable": true, "example": "1552de31-004a-42e5-82fa-7d9daf3ac367"}, "status": {"$ref": "#/components/schemas/PaymentStatusType"}, "message": {"type": "string", "description": "Message from the payment system.", "nullable": true, "example": "Mastercard Identity Check in progress."}, "statusPayload": {"$ref": "#/components/schemas/PaymentExtensionDataDto"}}, "additionalProperties": false, "description": "The payment request for a Shoppers basket."}, "PaymentStatusType": {"enum": [1, 2, 3], "type": "integer", "description": "\r\n\r\n1 = Pending\r\n\r\n2 = Success\r\n\r\n3 = Failed", "format": "int32", "x-enumNames": ["Pending", "Success", "Failed"]}, "PutEndShoppingTripResponseDto": {"required": ["cart", "endShoppingTripBarcode", "id", "paymentAllowedStatus", "shopper", "shoppingTripStatus"], "type": "object", "properties": {"id": {"type": "string", "description": "Gets or sets the id."}, "shopper": {"$ref": "#/components/schemas/ShopperDto"}, "cart": {"$ref": "#/components/schemas/CartDto"}, "endShoppingTripBarcode": {"type": "string", "description": "Gets or sets the end shopping trip barcode."}, "paymentAllowedStatus": {"$ref": "#/components/schemas/ShoppingTripPaymentAllowedStatusDto"}, "paymentBlockedReason": {"$ref": "#/components/schemas/ShoppingTripPaymentBlockedReasonDto"}, "paymentReference": {"type": "string", "description": "Gets or sets the payment reference.", "nullable": true}, "shoppingTripStatus": {"$ref": "#/components/schemas/ShoppingTripStatusDto"}}, "additionalProperties": false, "description": "Represents a shopping trip."}, "ShopperDto": {"required": ["firstName", "identifier"], "type": "object", "properties": {"identifier": {"type": "string", "description": "Gets or sets the identifier."}, "firstName": {"type": "string", "description": "Gets or sets the first name."}, "lastName": {"type": "string", "description": "Gets or sets the last name.", "nullable": true}, "extensionData": {"$ref": "#/components/schemas/ExtensionDataDto"}}, "additionalProperties": false, "description": "Represents a shopper."}, "ShoppingTripDto": {"required": ["cart", "endShoppingTripBarcode", "id", "shopper"], "type": "object", "properties": {"id": {"type": "string", "description": "Gets or sets the id."}, "shopper": {"$ref": "#/components/schemas/ShopperDto"}, "cart": {"$ref": "#/components/schemas/CartDto"}, "endShoppingTripBarcode": {"type": "string", "description": "Gets or sets the end shopping trip barcode."}}, "additionalProperties": false, "description": "Represents a shopping trip."}, "ShoppingTripPaymentAllowedStatusDto": {"enum": [0, 1, 2, 3], "type": "integer", "description": "Identifies the payment allowed status of a shopping trip.\r\n\r\n0 = Pending\r\n\r\n1 = Allowed\r\n\r\n2 = Blocked\r\n\r\n3 = PendingExpired", "format": "int32", "x-enumNames": ["Pending", "Allowed", "Blocked", "PendingExpired"]}, "ShoppingTripPaymentBlockedReasonDto": {"enum": [0, 1, 2, 3], "type": "integer", "description": "Indentifies the reason why payment allowed status is blocked.\r\n\r\n0 = BasketControlFailed\r\n\r\n1 = AgeVerificationFailed\r\n\r\n2 = AgeVerificationDisabled\r\n\r\n3 = ExternalEndOfTrip", "format": "int32", "x-enumNames": ["BasketControlFailed", "AgeVerificationFailed", "AgeVerificationDisabled", "ExternalEndOfTrip"]}, "ShoppingTripStatusDto": {"enum": [0, 1, 2, 3, 4, 5, 6, -1], "type": "integer", "description": "Identifies the status of the shopping trip.\r\n\r\n0 = Active\r\n\r\n1 = AllowNewDevice\r\n\r\n2 = WaitingForPayment\r\n\r\n3 = Completed\r\n\r\n4 = Abandoned\r\n\r\n5 = Cancelled\r\n\r\n6 = NoPayment\r\n\r\n-1 = WaitingForStart", "format": "int32", "x-enumNames": ["Active", "AllowNewDevice", "WaitingForPayment", "Completed", "Abandoned", "Cancelled", "NoPayment", "WaitingForStart"]}, "SignInResult": {"type": "object", "properties": {"authenticationScheme": {"type": "string", "nullable": true}, "principal": {"$ref": "#/components/schemas/ClaimsPrincipal"}, "properties": {"$ref": "#/components/schemas/AuthenticationProperties"}}, "additionalProperties": false}, "TimeRequirementDto": {"type": "object", "properties": {"start": {"type": "string", "description": "Gets or sets the start.", "format": "date-time", "nullable": true}, "end": {"type": "string", "description": "Gets or sets the end.", "format": "date-time", "nullable": true}, "messages": {"type": "array", "items": {"$ref": "#/components/schemas/LocalizedMessageDto"}, "description": "Gets or sets the messages.", "nullable": true}}, "additionalProperties": false, "description": "Represents a time requirement."}, "UnitOfMeasureDto": {"enum": [0, 1, 2, 3, 4, 5, 6], "type": "integer", "description": "Identifies the unit of measure.\r\n\r\n0 = Piece\r\n\r\n1 = Kilogram\r\n\r\n2 = Liter\r\n\r\n3 = Gram\r\n\r\n4 = Meter\r\n\r\n5 = Meter2\r\n\r\n6 = Hour", "format": "int32", "x-enumNames": ["Piece", "Kilogram", "Liter", "Gram", "<PERSON>er", "Meter2", "Hour"]}, "VatDto": {"required": ["percent", "total"], "type": "object", "properties": {"percent": {"type": "number", "description": "Gets or sets the percent.", "format": "double"}, "total": {"type": "number", "description": "Gets or sets the total.", "format": "double"}}, "additionalProperties": false, "description": "Represents value added tax (VAT)."}}, "securitySchemes": {"oauth2": {"type": "oauth2", "flows": {"clientCredentials": {"tokenUrl": "/api/connect/token", "scopes": {"openid": ""}}}}}}, "security": [{"oauth2": ["openid", "email", "profile", "offline_access"]}]}