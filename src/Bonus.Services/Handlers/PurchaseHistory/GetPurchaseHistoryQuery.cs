using Bonus.Adapters.LSRetail;
using Bonus.Adapters.LSRetail.Requests;
using Bonus.Shared.Types.Interfaces;
using MediatR;

namespace Bonus.Services.Handlers.PurchaseHistory;

public class GetPurchaseHistoryQuery(ILSRetailAdapter lsRetailAdapter, IBonusUser bonusUser) 
    : IRequestHandler<GetPurchaseHistoryRequest, GetPurchaseHistoryResponse>
{
    public async Task<GetPurchaseHistoryResponse> Handle(GetPurchaseHistoryRequest request, CancellationToken cancellationToken)
    {
        var salesEntriesRequest = new LSGetSalesEntriesGetByCardIdRequest
        {
            CardId = bonusUser.CardId!,
            MaxNumberOfTransactions = request.MaxNumberOfTransactions ?? 100
        };

        var salesEntriesResponse = await lsRetailAdapter.GetSalesEntriesByCardId(salesEntriesRequest, cancellationToken);

        var purchaseHistory = salesEntriesResponse.Response!.SalesEntries
            .Select(x => new ResultPurchaseHistory
            {
                Id = x.Id,
                PurchaseDate = x.DocumentRegTime.UtcDateTime,
                TotalAmount = x.TotalAmount,
                Title = x.StoreName,
                QrCode = x.ReceiptNumber, // TODO: implement QR code generation,
                Items = []
            })
            .ToList();

        return new GetPurchaseHistoryResponse
        {
            PurchaseHistory = purchaseHistory
        };
    }
}

public class GetPurchaseHistoryRequest : IRequest<GetPurchaseHistoryResponse>
{
    public int? MaxNumberOfTransactions { get; set; }
}

public class GetPurchaseHistoryResponse
{
    public required List<ResultPurchaseHistory> PurchaseHistory { get; set; } = [];
}

public class ResultPurchaseHistory
{
    public required string Id { get; set; }

    public required DateTime PurchaseDate { get; set; }

    public required decimal TotalAmount { get; set; }

    public required string Title { get; set; }

    public required string QrCode { get; set; }
    
    public required List<PurchaseHistoryItemDetail> Items { get; set; } = [];
}

public class PurchaseHistoryItemDetail
{
    public required string Id { get; set; }

    public required string Name { get; set; }

    public required string ItemId { get; set; }

    public required string ImageUrl { get; set; }

    public required decimal Quantity { get; set; }

    public required decimal Price { get; set; }
}