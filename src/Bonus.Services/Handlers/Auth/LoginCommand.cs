using System.Net.Mime;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using Bonus.Adapters.LSRetail;
using Bonus.Adapters.LSRetail.Enums;
using Bonus.Adapters.LSRetail.Models.Members;
using Bonus.Adapters.LSRetail.Requests;
using Bonus.Core.Data;
using Bonus.Core.Data.Entities;
using Bonus.Core.Services;
using Bonus.Core.Types.Common.Enums;
using Bonus.Resources;
using Bonus.Shared.Configuration.Settings;
using Bonus.Shared.Helpers;
using Bonus.Shared.Types.Common.Exceptions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;

namespace Bonus.Services.Handlers.Auth;

[Obsolete("Use Hagarid commands instead. This is useful for logging in with test user.")]
public class LoginCommand(
    BonusContext bonusContext, 
    ILSRetailAdapter lsRetailAdapter,
    IAuthenticationService authenticationService,
    ISystemTime systemTime,
    IOptionsSnapshot<BonusApiSettings> bonusApiSettings)
    : IRequestHandler<LoginRequest, LoginResponse>
{
    public async Task<LoginResponse> Handle(LoginRequest request, CancellationToken cancellationToken)
    {
        if (request.PhoneNumber.HasValue() is false && request.Ssn.HasValue() is false)
        {
            throw new BonusException(nameof(Translations.Auth_PhoneOrSsnRequired));
        }

        if (request.DeviceId.HasValue() is false)
        {
            await CreateAuthenticationLog(request, null, "Device ID is missing");
            await bonusContext.SaveChangesAsync();
            throw new BonusException(nameof(Translations.Auth_DeviceIdRequired));
        }

        var settingsRequest = new LSScanPayGoProfileRequest
        {
            ProfileId = "",
            StoreId = "",
        };
        
        var settings = await lsRetailAdapter.GetScanPayGoProfile(settingsRequest, cancellationToken);

        var testUserEnabled = settings.Response!.ScanPayGoProfile.Flags.Flags.FirstOrDefault(x => x.Name == FeatureFlagName.AudkenniTestUserEnabled)?.Value;
        var testUserIdentifier = settings.Response!.ScanPayGoProfile.Flags.Flags.FirstOrDefault(x => x.Name == FeatureFlagName.AudkenniTestUser)?.Value;
        var testUserCardId = settings.Response!.ScanPayGoProfile.Flags.Flags.FirstOrDefault(x => x.Name == FeatureFlagName.AudkenniTestCardId)?.Value;
        var testLoginRequested = testUserIdentifier == request.PhoneNumber || testUserIdentifier == request.Ssn;
        
        if (testUserEnabled == "true" && testLoginRequested && testUserIdentifier.HasValue() && testUserCardId.HasValue())
        {
            var getTestContactRequest = new LSGetContactGetByCardIdRequest
            {
                CardId = bonusApiSettings.Value.TestCardIdOverride.HasValue() ? bonusApiSettings.Value.TestCardIdOverride : testUserCardId!,
                NumberOfTransactionsReturned = 0,
            };
            
            var testUserResponse = await lsRetailAdapter.GetContactByCardId(getTestContactRequest, cancellationToken);

            if (testUserResponse.Response?.Contact is null)
            {
                await CreateAuthenticationLog(request, null, "User not found");
                await bonusContext.SaveChangesAsync();
                throw new BonusException(nameof(Translations.Login_UserNotFound));
            }

            return await CreateLoginResponse(request, testUserResponse.Response!.Contact);
        }
        
        var audkenniId = await DoAudkenniLogin(settings.Response!.ScanPayGoProfile, request.PhoneNumber, request.Ssn, request.DeviceId, cancellationToken);

        if (audkenniId.HasValue() is false)
        {
            await CreateAuthenticationLog(request, null, "Audkenni login failed");
            await bonusContext.SaveChangesAsync();
            throw new BonusException(nameof(Translations.Login_AudkenniFailed));
        }

        var socialLogonRequest = new LSSocialLogonRequest
        {
            Authenticator = "Audkenni",
            AuthenticationId = audkenniId!,
            DeviceId = request.DeviceId!,
            DeviceName = null,
            IncludeDetails = true,
        };

        var socialLogonResponse = await lsRetailAdapter.SocialLogon(socialLogonRequest, cancellationToken);
        if (socialLogonResponse.Response?.Contact is null)
        {
            await CreateAuthenticationLog(request, null, "User not found");
            await bonusContext.SaveChangesAsync();
            throw new BonusException(nameof(Translations.Login_UserNotFound));
        }

        return await CreateLoginResponse(request, socialLogonResponse.Response!.Contact);
    }

    private async Task<LoginResponse> CreateLoginResponse(LoginRequest request, MemberContact contact)
    {
        var user = await bonusContext.Users
            .Where(x => x.MemberContactId == contact.Id)
            .Where(x => x.AccountId == contact.Account!.Id)
            .Where(x => x.CardId == contact.Cards.First().Id)
            .Include(x => x.RefreshTokens)
            .FirstOrDefaultAsync();
        
        if (user is not null)
        {
            foreach (var refreshToken in user.RefreshTokens.Where(x => x.UsedOn is null))
            {
                refreshToken.ArchivedTime = systemTime.UtcNow;
            }

            return await CreateTokenLoginResponse(user.Id, user.CardId, true, await CreateAuthenticationLog(request, user.Id));
        }

        // We wouldn't need transaction if userId was not stored in claims, but it's a matter of time when we will have to use userId.
        await using var transaction = await bonusContext.Database.BeginTransactionAsync();
        
        user = new User
        {
            MemberContactId = contact.Id,
            AccountId = contact.Account!.Id,
            CardId = contact.Cards.First().Id,
            AuthenticationId = contact.AuthenticationId,
            Ssn = request.Ssn,
        };
            
        await bonusContext.Users.AddAsync(user);
        await bonusContext.SaveChangesAsync();

        var response = await CreateTokenLoginResponse(user.Id, user.CardId, true, await CreateAuthenticationLog(request, user.Id));
        await transaction.CommitAsync();

        return response;
    }

    private async Task<LoginResponse> CreateTokenLoginResponse(int userId, string cardId, bool termsAccepted, AuthenticationLog authenticationLog)
    {
        var loginData = await authenticationService.CreateToken(userId, cardId, termsAccepted, authenticationLog);
        await bonusContext.SaveChangesAsync();
        
        return new LoginResponse
        {
            Token = loginData.Token,
            RefreshToken = loginData.RefreshToken
        };
    }

    private async Task<AuthenticationLog> CreateAuthenticationLog(LoginRequest request, int? userId = null, string? errorMessage = null)
    {
        return await authenticationService.CreateAuthenticationLog(request.PhoneNumber ?? request.Ssn!, request.PhoneNumber.HasValue() ? CredentialType.PhoneNumber : CredentialType.Ssn, userId, errorMessage, request.IpAddress, request.DeviceManufacturer, request.DeviceModelName, request.DeviceId, request.AppVersion, request.OperatingSystem, request.OperatingSystemVersion);
    }

    private async Task<string?> DoAudkenniLogin(ScanPayGoProfileGetResult settings, string? phoneNumber, string? ssn, string? deviceId, CancellationToken cancellationToken)
    {
        var audkenniUrl = settings.Flags.Flags.FirstOrDefault(x => x.Name == FeatureFlagName.AudkenniBaseURL)?.Value;
        var audkenniClientId = settings.Flags.Flags.FirstOrDefault(x => x.Name == FeatureFlagName.AudkenniClientId)?.Value;
        var audkenniSecret = settings.Flags.Flags.FirstOrDefault(x => x.Name == FeatureFlagName.AudkenniSecret)?.Value;
        var audkenniRedirectUrl = settings.Flags.Flags.FirstOrDefault(x => x.Name == FeatureFlagName.AudkenniRedirectURL)?.Value;
        var audkenniMessage = settings.Flags.Flags.FirstOrDefault(x => x.Name == FeatureFlagName.AudkenniMessageToUser)?.Value;
        
        if (audkenniUrl.HasValue() is false || 
            audkenniClientId.HasValue() is false || 
            audkenniSecret.HasValue() is false || 
            audkenniRedirectUrl.HasValue() is false)
        {
            throw new BonusException(nameof(Translations.Login_AudkenniSettingsNotFound));
        }

        using var client = new HttpClient();

        var step1Result = await InitiateAuthentication(client, audkenniUrl!, cancellationToken);
        if (step1Result is null)
        {
            return null;
        }

        var step2Result = await SubmitUserInfo(client, audkenniUrl!, step1Result, audkenniClientId!, phoneNumber, ssn, audkenniMessage, cancellationToken);

        if (step2Result is null)
        {
            return null;
        }

        var step3Result = await PollForAuthentication(client, audkenniUrl!, step2Result, cancellationToken);
        if (step3Result is null || step3Result.TokenId.HasValue() is false)
        {
            return null;
        }

        var (codeVerifier, codeChallenge) = CreateCodeVerifierAndChallenge();

        var authCode = await GetAuthorizationCode(client, audkenniUrl!, audkenniClientId!, audkenniRedirectUrl!, codeChallenge, step3Result.TokenId, cancellationToken);

        if (authCode is null)
        {
            return null;
        }

        var step5Result = await GetAccessToken(client, audkenniUrl!, audkenniClientId!, audkenniRedirectUrl!, codeVerifier, authCode, audkenniSecret!, step3Result.TokenId, cancellationToken);

        if (step5Result is null)
        {
            return null;
        }

        var userInfo = await GetUserInfo(client, audkenniUrl!, step5Result.AccessToken, step3Result.TokenId, cancellationToken);

        if (userInfo is null)
        {
            return null;
        }

        return userInfo.NationalRegisterId;
    }

    private async Task<AudkenniStep1Response?> InitiateAuthentication(HttpClient client, string audkenniUrl, CancellationToken cancellationToken)
    {
        var request = new HttpRequestMessage(HttpMethod.Post, $"{audkenniUrl}/sso/json/realms/root/realms/audkenni/authenticate?authIndexType=service&authIndexValue=api_v100");
            
        request.Headers.Add("Accept", "application/json");
        request.Headers.Add("Accept-API-Version", "resource=2.0,protocol=1.0");
        request.Content = new StringContent("{}", Encoding.UTF8, MediaTypeNames.Application.Json);
        
        var response = await client.SendAsync(request, cancellationToken);
        if (response.IsSuccessStatusCode is false)
        {
            return null;
        }

        var content = await response.Content.ReadAsStringAsync(cancellationToken);
        return JsonSerializer.Deserialize<AudkenniStep1Response>(content);
    }

    private async Task<string?> SubmitUserInfo(HttpClient client, string audkenniUrl, AudkenniStep1Response step1Response, string clientId, string? phoneNumber, string? ssn, string? messageToUser, CancellationToken cancellationToken)
    {
        // We'll build the request content based on the step1 response
        if (step1Response.Callbacks.Count < 7)
        {
            return null;
        }

        // Set the client ID
        step1Response.Callbacks[0].Input[0].Value = clientId;
        step1Response.Callbacks[1].Input[0].Value = "";
        
        // Set identification info based on what was provided
        if (phoneNumber.HasValue())
        {
            step1Response.Callbacks[2].Input[0].Value = phoneNumber;
            step1Response.Callbacks[6].Input[0].Value = 0; // Phone auth mode
        }
        else if (ssn.HasValue())
        {
            // Generate hash for SSN authentication
            string hashValue = ComputeSha512Hash(ssn!);
            
            step1Response.Callbacks[2].Input[0].Value = ssn;
            step1Response.Callbacks[5].Input[0].Value = hashValue;
            step1Response.Callbacks[6].Input[0].Value = 2; // SSN auth mode
        }
        else
        {
            return null;
        }
        
        // Set message and other standard values
        step1Response.Callbacks[3].Input[0].Value = messageToUser ?? "Sign in to your account";
        step1Response.Callbacks[4].Input[0].Value = "false";

        // Serialize and send the request
        var authStep1Json = JsonSerializer.Serialize(step1Response);

        var request = new HttpRequestMessage(HttpMethod.Post, $"{audkenniUrl}/sso/json/realms/root/realms/audkenni/authenticate?authIndexType=service&authIndexValue=api_v100");
            
        request.Headers.Add("Accept", "application/json");
        request.Headers.Add("Accept-API-Version", "resource=2.0,protocol=1.0");
        request.Headers.Add("Cookie", "audssossolb=03");
        request.Content = new StringContent(authStep1Json, Encoding.UTF8, MediaTypeNames.Application.Json);
        
        var response = await client.SendAsync(request, cancellationToken);
        if (response.IsSuccessStatusCode is false)
        {
            return null;
        }

        return await response.Content.ReadAsStringAsync(cancellationToken);
    }

    private async Task<AudkenniStep3Response?> PollForAuthentication(HttpClient client, string audkenniUrl, string step2Content, CancellationToken cancellationToken)
    {
        AudkenniStep3Response? step3Response = null;
        var maxAttempts = 30;
        
        for (var i = 0; i < maxAttempts; i++)
        {
            await Task.Delay(2000, cancellationToken);
            
            var request = new HttpRequestMessage(HttpMethod.Post, $"{audkenniUrl}/sso/json/realms/root/realms/audkenni/authenticate");
                
            request.Headers.Add("Accept", "application/json");
            request.Headers.Add("Accept-API-Version", "resource=2.0,protocol=1.0");
            request.Headers.Add("Cookie", "audssossolb=03");
            request.Content = new StringContent(step2Content, Encoding.UTF8, MediaTypeNames.Application.Json);
            
            var response = await client.SendAsync(request, cancellationToken);
            
            // If user denied the request
            if (response.StatusCode == System.Net.HttpStatusCode.Unauthorized)
            {
                break;
            }
                
            var content = await response.Content.ReadAsStringAsync(cancellationToken);
            step3Response = JsonSerializer.Deserialize<AudkenniStep3Response>(content);
            
            // If we have a token, we're done polling
            if (step3Response != null && step3Response.TokenId.HasValue())
            {
                break;
            }
        }
        
        return step3Response;
    }

    private (string codeVerifier, string codeChallenge) CreateCodeVerifierAndChallenge()
    {
        // Generate code verifier
        var rng = RandomNumberGenerator.Create();
        var bytes = new byte[32];
        rng.GetBytes(bytes);

        var codeVerifier = Convert.ToBase64String(bytes)
            .TrimEnd('=')
            .Replace('+', '-')
            .Replace('/', '_');

        // Generate code challenge
        using var sha256 = SHA256.Create();
        var challengeBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(codeVerifier));
        var codeChallenge = Convert.ToBase64String(challengeBytes)
            .TrimEnd('=')
            .Replace('+', '-')
            .Replace('/', '_');

        return (codeVerifier, codeChallenge);
    }

    private async Task<string?> GetAuthorizationCode(HttpClient client, string audkenniUrl, string clientId, string redirectUrl, string codeChallenge, string tokenId, CancellationToken cancellationToken)
    {
        var url = $"{audkenniUrl}/sso/oauth2/realms/root/realms/audkenni/authorize?service=api_v100" +
                 $"&client_id={clientId}" +
                 $"&response_type=code" +
                 $"&scope=openid profile signature" +
                 $"&code_challenge={codeChallenge}" +
                 $"&code_challenge_method=S256" +
                 $"&state=123abc" +
                 $"&redirect_uri={redirectUrl}";

        var request = new HttpRequestMessage(HttpMethod.Get, url);
        request.Headers.Add("Cookie", $"audsso={tokenId}; audssossolb=03");
        request.Headers.Add("Content-Type", "application/x-www-form-urlencoded");
        
        var response = await client.SendAsync(request, cancellationToken);
        
        // Check for Location header containing the code
        if (response.Headers.Location is null)
        {
            return null;
        }
        
        var locationUri = response.Headers.Location;
        var queryParams = System.Web.HttpUtility.ParseQueryString(locationUri.Query);
        return queryParams["code"];
    }

    private async Task<AudkenniStep5Response?> GetAccessToken(HttpClient client, string audkenniUrl, string clientId, string redirectUrl, string codeVerifier, string code, string clientSecret, string tokenId, CancellationToken cancellationToken)
    {
        var formContent = new FormUrlEncodedContent([
            new KeyValuePair<string, string>("grant_type", "authorization_code"),
            new KeyValuePair<string, string>("client_id", clientId),
            new KeyValuePair<string, string>("redirect_uri", redirectUrl),
            new KeyValuePair<string, string>("code_verifier", codeVerifier),
            new KeyValuePair<string, string>("code", code),
            new KeyValuePair<string, string>("client_secret", clientSecret)
        ]);
        
        var request = new HttpRequestMessage(HttpMethod.Post, $"{audkenniUrl}/sso/oauth2/realms/root/realms/audkenni/access_token");
            
        request.Headers.Add("Cookie", $"audssossolb=03; audsso={tokenId}");
        request.Content = formContent;
        
        var response = await client.SendAsync(request, cancellationToken);
        if (response.IsSuccessStatusCode is false)
        {
            return null;
        }

        var content = await response.Content.ReadAsStringAsync(cancellationToken);
        return JsonSerializer.Deserialize<AudkenniStep5Response>(content);
    }

    private async Task<AudkenniStep6Response?> GetUserInfo(HttpClient client, string audkenniUrl, string accessToken, string tokenId, CancellationToken cancellationToken)
    {
        var request = new HttpRequestMessage(HttpMethod.Post, $"{audkenniUrl}/sso/oauth2/realms/root/realms/audkenni/userinfo");
            
        request.Headers.Add("Content-Type", "application/x-www-form-urlencoded");
        request.Headers.Add("Authorization", $"Bearer {accessToken}");
        request.Headers.Add("Cookie", $"audssossolb=03; audsso={tokenId}");
        
        var response = await client.SendAsync(request, cancellationToken);
        if (response.IsSuccessStatusCode is false)
        {
            return null;
        }

        var content = await response.Content.ReadAsStringAsync(cancellationToken);
        return JsonSerializer.Deserialize<AudkenniStep6Response>(content);
    }
    
    private string ComputeSha512Hash(string input)
    {
        using var sha = SHA512.Create();
        var bytes = Encoding.UTF8.GetBytes(input);
        var hash = sha.ComputeHash(bytes);
        return Convert.ToBase64String(hash);
    }
}

public class LoginRequest : IRequest<LoginResponse>
{
    public required string? PhoneNumber { get; set; }
    
    public required string? Ssn { get; set; }
    
    public required string? DeviceId { get; set; }
    
    public required string? DeviceManufacturer { get; set; }
    
    public required string? DeviceModelName { get; set; }
    
    public required string? OperatingSystem { get; set; }
    
    public required string? OperatingSystemVersion { get; set; }
    
    public required string? AppVersion { get; set; }
    
    public required string IpAddress { get; set; }
}

public class LoginResponse
{
    public required string Token { get; set; }
    
    public required string RefreshToken { get; set; }
}

// Model classes for Audkenni authentication flow
public class AudkenniStep1Response
{
    [JsonPropertyName("authId")]
    public string AuthId { get; set; } = string.Empty;
    
    [JsonPropertyName("callbacks")]
    public List<AudkenniCallback> Callbacks { get; set; } = [];
}

public class AudkenniCallback
{
    [JsonPropertyName("type")]
    public string Type { get; set; } = string.Empty;
    
    [JsonPropertyName("input")]
    public List<AudkenniInput> Input { get; set; } = [];
    
    [JsonPropertyName("_id")]
    public int Id { get; set; }
}

public class AudkenniInput
{
    [JsonPropertyName("name")]
    public string Name { get; set; } = string.Empty;
    
    [JsonPropertyName("value")]
    public object? Value { get; set; }
}

public class AudkenniStep3Response
{
    [JsonPropertyName("tokenId")]
    public string TokenId { get; set; } = string.Empty;
    
    [JsonPropertyName("successUrl")]
    public string SuccessUrl { get; set; } = string.Empty;
    
    [JsonPropertyName("realm")]
    public string Realm { get; set; } = string.Empty;
}

public class AudkenniStep5Response
{
    [JsonPropertyName("access_token")]
    public string AccessToken { get; set; } = string.Empty;
    
    [JsonPropertyName("scope")]
    public string Scope { get; set; } = string.Empty;
    
    [JsonPropertyName("id_token")]
    public string IdToken { get; set; } = string.Empty;
    
    [JsonPropertyName("token_type")]
    public string TokenType { get; set; } = string.Empty;
    
    [JsonPropertyName("expires_in")]
    public int ExpiresIn { get; set; }
}

public class AudkenniStep6Response
{
    [JsonPropertyName("signature")]
    public string Signature { get; set; } = string.Empty;
    
    [JsonPropertyName("documentNr")]
    public string DocumentNumber { get; set; } = string.Empty;
    
    [JsonPropertyName("certificate")]
    public string Certificate { get; set; } = string.Empty;
    
    [JsonPropertyName("nationalRegisterId")]
    public string NationalRegisterId { get; set; } = string.Empty;
    
    [JsonPropertyName("name")]
    public string Name { get; set; } = string.Empty;
    
    [JsonPropertyName("sub")]
    public string Subject { get; set; } = string.Empty;
}