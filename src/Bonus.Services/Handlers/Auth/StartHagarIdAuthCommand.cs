using Bonus.Core.Data;
using Bonus.Core.Data.Entities;
using Bonus.Core.Services;
using Bonus.Core.Types.Common.Enums;
using Bonus.Resources;
using Bonus.Services.Services;
using Bonus.Shared.Helpers;
using Bonus.Shared.Types.Common.Exceptions;
using MediatR;

namespace Bonus.Services.Handlers.Auth;

public class StartHagarIdAuthCommand(IHagarIdAuthenticationService hagarIdAuthenticationService, IAuthenticationService authenticationService, BonusContext bonusContext, ISystemTime systemTime)
    : IRequestHandler<StartHagarIdAuthRequest, StartHagarIdAuthResponse>
{
    public async Task<StartHagarIdAuthResponse> Handle(StartHagarIdAuthRequest request, CancellationToken cancellationToken)
    {
        if (request.PhoneNumber.HasValue() is false && request.NationalId.HasValue() is false)
        {
            throw new BonusException(nameof(Translations.Auth_PhoneOrSsnRequired));
        }

        // Use NationalId if provided, otherwise use PhoneNumber
        var identifier = request.NationalId ?? request.PhoneNumber!;
        var credentialType = request.NationalId.HasValue() ? CredentialType.Ssn : CredentialType.PhoneNumber;
        var response = await hagarIdAuthenticationService.StartAuthentication(identifier, credentialType, cancellationToken);

        if (response == null || response.SessionId.HasValue() is false)
        {
            throw new BonusException(nameof(Translations.Login_HagarIdFailed));
        }

        var expiresAt = systemTime.UtcNow.AddMinutes(10); // 10 minute expiry
        var hagarIdSession = new HagarIdSession
        {
            SessionId = response.SessionId!,
            ExpiresAt = expiresAt,
            IsCompleted = false
        };

        await bonusContext.HagarIdSessions.AddAsync(hagarIdSession, cancellationToken);
        await bonusContext.SaveChangesAsync(cancellationToken);
        
        await authenticationService.CreateAuthenticationLog(
            identifier,
            credentialType,
            null,
            null,
            request.IpAddress,
            request.DeviceManufacturer,
            request.DeviceModelName,
            request.DeviceId,
            request.AppVersion,
            request.OperatingSystem,
            request.OperatingSystemVersion,
            hagarIdSession.Id);

        await bonusContext.SaveChangesAsync(cancellationToken);

        return new StartHagarIdAuthResponse
        {
            SessionId = response.SessionId!,
            AuthCode = response.AuthCode!
        };
    }
}

public class StartHagarIdAuthRequest : IRequest<StartHagarIdAuthResponse>
{
    public string? PhoneNumber { get; set; }
    public string? NationalId { get; set; }
    public required string? DeviceId { get; set; }
    public required string? DeviceManufacturer { get; set; }
    public required string? DeviceModelName { get; set; }
    public required string? OperatingSystem { get; set; }
    public required string? OperatingSystemVersion { get; set; }
    public required string? AppVersion { get; set; }
    public required string IpAddress { get; set; }
}

public class StartHagarIdAuthResponse
{
    public required string SessionId { get; set; }
    
    public required string AuthCode { get; set; }
}
