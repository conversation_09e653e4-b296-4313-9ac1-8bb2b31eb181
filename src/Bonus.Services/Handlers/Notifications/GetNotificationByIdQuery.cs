using Bonus.Core.Data;
using Bonus.Resources;
using Bonus.Shared.Types.Common.Exceptions;
using Bonus.Shared.Types.Interfaces;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Bonus.Services.Handlers.Notifications;

public class GetNotificationByIdQuery(BonusContext bonusContext, IBonusUser bonusUser)
    : IRequestHandler<GetNotificationByIdRequest, GetNotificationByIdResponse>
{
    public async Task<GetNotificationByIdResponse> Handle(GetNotificationByIdRequest request, CancellationToken cancellationToken)
    {
        // It's actually notification recipient id, but clients that are accessing api don't need to know that...
        var notification = await bonusContext.NotificationRecipients
            .Where(nr => nr.Id == request.Id)
            .Where(nr => nr.UserId == bonusUser.UserId)
            .Select(nr => new NotificationDto
            {
                Id = nr.Id,
                Title = nr.Notification.Title,
                Description = nr.Notification.Description,
                CreatedAt = nr.Notification.CreatedTime.ToString("o"),
                Read = nr.ReadTime != null
            })
            .FirstOrDefaultAsync(cancellationToken);

        if (notification is null)
        {
            throw new BonusException(nameof(Translations.Notification_NotFound));
        }

        return new GetNotificationByIdResponse
        {
            Notification = notification
        };
    }
}

public class GetNotificationByIdRequest : IRequest<GetNotificationByIdResponse>
{
    public required long Id { get; set; } // notification recipient id
}

public class GetNotificationByIdResponse
{
    public required NotificationDto Notification { get; set; }
}
