using Bonus.Core.Data;
using Bonus.Shared.Types.Interfaces;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Bonus.Services.Handlers.Notifications;

public class GetNotificationsQuery(BonusContext dbContext, IBonusUser bonusUser)
    : IRequestHandler<GetNotificationsRequest, GetNotificationsResponse>
{
    public async Task<GetNotificationsResponse> Handle(GetNotificationsRequest request, CancellationToken cancellationToken)
    {
        var notifications = await dbContext.NotificationRecipients
            .Where(nr => nr.UserId == bonusUser.UserId)
            .OrderByDescending(nr => nr.Notification.CreatedTime)
            .Select(nr => new NotificationDto
            {
                Id = nr.Id,
                Title = nr.Notification.Title,
                Description = nr.Notification.Description,
                CreatedAt = nr.Notification.CreatedTime.ToString("o"),
                Read = nr.ReadTime != null
            })
            .ToListAsync(cancellationToken);

        return new GetNotificationsResponse
        {
            Notifications = notifications
        };
    }
}

public class GetNotificationsRequest : IRequest<GetNotificationsResponse>;

public class GetNotificationsResponse
{
    public required List<NotificationDto> Notifications { get; set; } = [];
}

public class NotificationDto
{
    public required int Id { get; set; }
    
    public required string Title { get; set; }
    
    public required string Description { get; set; }
    
    public required string CreatedAt { get; set; }
    
    public required bool Read { get; set; }
}