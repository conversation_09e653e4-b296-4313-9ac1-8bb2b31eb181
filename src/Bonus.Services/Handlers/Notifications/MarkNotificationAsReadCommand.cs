using Bonus.Core.Data;
using Bonus.Core.Services;
using Bonus.Resources;
using Bonus.Shared.Types.Common.Exceptions;
using Bonus.Shared.Types.Interfaces;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Bonus.Services.Handlers.Notifications;

public class MarkNotificationAsReadCommand(BonusContext dbContext, IBonusUser bonusUser, ISystemTime systemTime)
    : IRequestHandler<MarkNotificationAsReadRequest, MarkNotificationAsReadResponse>
{
    public async Task<MarkNotificationAsReadResponse> Handle(MarkNotificationAsReadRequest request, CancellationToken cancellationToken)
    {
        var notificationRecipient = await dbContext.NotificationRecipients
            .Where(nr => nr.Id == request.Id)
            .Where(nr => nr.UserId == bonusUser.UserId)
            .FirstOrDefaultAsync(cancellationToken);

        if (notificationRecipient is null)
        {
            throw new BonusException(nameof(Translations.Notification_NotFound));
        }

        notificationRecipient.ReadTime = systemTime.UtcNow;
        await dbContext.SaveChangesAsync(cancellationToken);

        return new MarkNotificationAsReadResponse();
    }
}

public class MarkNotificationAsReadRequest : IRequest<MarkNotificationAsReadResponse>
{
    public required int Id { get; set; }
}

public class MarkNotificationAsReadResponse
{

}