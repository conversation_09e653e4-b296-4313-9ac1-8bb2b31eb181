using Bonus.Core.Data;
using Bonus.Core.Data.Entities;
using Bonus.Resources;
using Bonus.Shared.Helpers;
using Bonus.Shared.Types.Common.Exceptions;
using MediatR;

namespace Bonus.Services.Handlers.Notifications;

public class CreateNotificationCommand(BonusContext context)
    : IRequestHandler<CreateNotificationRequest, CreateNotificationResponse>
{
    public async Task<CreateNotificationResponse> Handle(CreateNotificationRequest request, CancellationToken cancellationToken)
    {
        if (request.Title.HasValue() is false)
        {
            throw new BonusException(nameof(Translations.Notification_TitleRequired));
        }

        if (request.Body.HasValue() is false)
        {
            throw new BonusException(nameof(Translations.Notification_BodyRequired));
        }

        if (request.UserIds is null || request.UserIds.Any() is false)
        {
            throw new BonusException(nameof(Translations.Notification_RecipientsCannotBeEmpty));
        }

        var notification = new Notification()
        {
            Title = request.Title,
            Description = request.Body
        };

        await context.Notifications.AddAsync(notification, cancellationToken);

        foreach (var userId in request.UserIds)
        {
            notification.Recipients.Add(new NotificationRecipient()
            {
                UserId = userId,
                Notification = notification
            });
        }
        
        await context.SaveChangesAsync();

        return new();
    }
}

public class CreateNotificationRequest : IRequest<CreateNotificationResponse>
{
    public required string Title { get; set; }
    
    public required string Body { get; set; }
    
    public required IEnumerable<int> UserIds { get; set; }
}

public class CreateNotificationResponse;