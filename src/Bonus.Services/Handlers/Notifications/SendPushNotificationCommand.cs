using Bonus.Adapters.Expo;
using Bonus.Core.Data;
using Bonus.Core.Services;
using Bonus.Resources;
using Bonus.Shared.Types.Common.Exceptions;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Bonus.Services.Handlers.Notifications;

public class SendPushNotificationCommand(IExpoPushServiceApi expoPushApi, BonusContext bonusContext, ISystemTime systemTime)
    : IRequestHandler<SendPushNotificationRequest>
{
    public async Task Handle(SendPushNotificationRequest request, CancellationToken cancellationToken)
    {
        var notification = await bonusContext.Notifications.Where(n => n.Id == request.NotificationId)
            .FirstOrDefaultAsync(cancellationToken);

        if (notification is null)
        {
            throw new BonusException(nameof(Translations.Notification_NotFound));
        }

        var pushTokens = notification.Recipients
            .Where(r => !string.IsNullOrEmpty(r.User.UserExternalNotificationId))
            .Select(r => r.User.UserExternalNotificationId!)
            .ToList();

        if (pushTokens.Any() is false)
        {
            throw new BonusException(nameof(Translations.Notification_PushTokenMissing));
        }
    
        var pushNotification = new PushNotification
        {
            Title = notification.Title,
            Body = notification.Description,
            Recipients = pushTokens,
            Priority = PushNotification.NotificationPriority.Default
        };

        var response = await expoPushApi.SendAsync(pushNotification);

        if (response.IsSuccessStatusCode)
        {
            notification.SentAt = systemTime.UtcNow;
            await bonusContext.SaveChangesAsync(cancellationToken);

            return;
        }

        throw new BonusException(nameof(Translations.Notification_FailedToSend), response.Error?.Content);

    }
}

public class SendPushNotificationRequest : IRequest
{
    public required int NotificationId { get; set; }
}
