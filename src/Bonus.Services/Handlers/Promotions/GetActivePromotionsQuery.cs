using Bonus.Core.Data;
using Bonus.Core.Services;
using Bonus.Shared.Helpers;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Bonus.Services.Handlers.Promotions;

public class GetActivePromotionsQuery(BonusContext bonusContext, ISystemTime systemTime)
    : IRequestHandler<GetActivePromotionsRequest, GetActivePromotionsResponse>
{
    public async Task<GetActivePromotionsResponse> Handle(GetActivePromotionsRequest request, CancellationToken cancellationToken)
    {
        var promotions = await bonusContext.Promotions
            .WhereCurrent(systemTime.UtcNow)
            .WhereIf(request.StoreId.HasValue(), x => x.LSRetailStoreId == request.StoreId || string.IsNullOrEmpty(x.LSRetailStoreId))
            .Select(x => new Promotion
            {
                Title = x.Title,
                Description = x.Description,
                Image = x.Base64Image,
                StoreSpecific = !string.IsNullOrEmpty(x.LSRetailStoreId),
                ValidFrom = x.Valid<PERSON>rom,
                ValidUntil = x.ValidTo
            })
            .ToListAsync(cancellationToken);

        return new GetActivePromotionsResponse
        {
            Promotions = promotions,
        };
    }
}

public class GetActivePromotionsRequest : IRequest<GetActivePromotionsResponse>
{
    public string? StoreId { get; set; }
}

public class GetActivePromotionsResponse
{
    public required List<Promotion> Promotions { get; set; }
}

public class Promotion
{
    public required string Title { get; set; }
    
    public required string? Description { get; set; }
    
    public required string Image { get; set; }
    
    public required bool StoreSpecific { get; set; }
    
    public required DateTime ValidFrom { get; set; }
    
    public required DateTime? ValidUntil { get; set; }
}