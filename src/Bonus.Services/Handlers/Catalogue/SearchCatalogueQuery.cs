using Bonus.Services.Services;
using MediatR;

namespace Bonus.Services.Handlers.Catalogue;

public class SearchCatalogueQuery(IHierarchyRawDataService hierarchyRawDataService, IProductSearchService searchService, IImageUrlService imageUrlService) : IRequestHandler<SearchCatalogueRequest, SearchCatalogueResponse>
{
    public async Task<SearchCatalogueResponse> Handle(SearchCatalogueRequest request, CancellationToken cancellationToken)
    {
        if (request.Limit == null || request.Limit < 1)
        {
            request.Limit = 10;
        }

        var itemIds = await searchService.SearchItemsAsync(request.Query, request.Limit.Value, cancellationToken);
        var products = await hierarchyRawDataService.GetAllProductsAsync(cancellationToken);

        var results = products
            .Where(x => itemIds.Any(i => i.Id == x.Id))
            .Select(x => new SearchResultItem
            {
                Id = x.Id,
                Name = x.Description!,
                ImageUrl = imageUrlService.CreateImageUrl(x.ImageId),
                Price = 1990,
                Measure = "N/A",
                CategoryId = x.ParentNode,
                CategoryName = x.ParentNode,
                Similarity = itemIds.FirstOrDefault(i => i.Id == x.Id)!.Similarity
            })
            .GroupBy(x => x.Id)
            .Select(g => g.First())
            .OrderByDescending(x => x.Similarity)
            .ToList();
        
        return new SearchCatalogueResponse
        {
            Items = results,
            TotalCount = results.Count
        };
    }
}

public class SearchCatalogueRequest : IRequest<SearchCatalogueResponse>
{
    public required string Query { get; set; }
    
    public int? Limit { get; set; }
}

public class SearchCatalogueResponse
{
    public required List<SearchResultItem> Items { get; set; } = [];
    
    public required int TotalCount { get; set; }
}

public class SearchResultItem // TODO: This can be either item or category? Or just item?
{
    public required string Id { get; set; }
    
    public required string Name { get; set; }
    
    public required string ImageUrl { get; set; }
    
    public required decimal? Price { get; set; }
    
    public required string? Measure { get; set; }
    
    public decimal? DiscountPrice { get; set; }
    
    public int? DiscountPercentage { get; set; }
    
    public required string? CategoryId { get; set; }
    
    public required string? CategoryName { get; set; }
    
    public float Similarity { get; set; }
}