using MediatR;

namespace Bonus.Services.Handlers.Catalogue;

public class GetFullCatalogueQuery : IRequestHandler<GetFullCatalogueRequest, GetFullCatalogueResponse>
{
    public async Task<GetFullCatalogueResponse> Handle(GetFullCatalogueRequest request, CancellationToken cancellationToken)
    {
        throw new NotImplementedException();
    }
}

public class GetFullCatalogueRequest : IRequest<GetFullCatalogueResponse>
{
    public DateTime? LastUpdated { get; set; }
}

public class GetFullCatalogueResponse
{
    public required List<CatalogueCategory> Categories { get; set; } = [];
    
    public required DateTime LastUpdated { get; set; }
    
    public required bool HasChanges { get; set; }
}

public class CatalogueCategory
{
    public required string Id { get; set; }
    
    public required string Name { get; set; }
    
    public required string ImageUrl { get; set; }
    
    public required List<CatalogueItem> Items { get; set; } = [];
    
    public required List<CatalogueCategory> SubCategories { get; set; } = [];
}

public class CatalogueItem
{
    public required string Id { get; set; }
    
    public required string Name { get; set; }
    
    public required string ImageUrl { get; set; }
    
    public required decimal Price { get; set; }
    
    public required string Measure { get; set; }
    
    public decimal? DiscountPrice { get; set; }
    
    public int? DiscountPercentage { get; set; }
    
    public required string CategoryId { get; set; }
}