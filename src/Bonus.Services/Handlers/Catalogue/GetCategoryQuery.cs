using Bonus.Services.Services;
using MediatR;

namespace Bonus.Services.Handlers.Catalogue;

public class GetCategoryQuery(ICatalogueService catalogueService)
    : IRequestHandler<GetCategoryRequest, GetCategoryResponse>
{
    public async Task<GetCategoryResponse> <PERSON>le(GetCategoryRequest request, CancellationToken cancellationToken)
    {
        var category = await catalogueService.GetCategoryByIdAsync(request.Id, cancellationToken);

        return new GetCategoryResponse
        {
            Id = category.Id,
            Name = category.Name,
            ImageUrl = category.ImageUrl,
            Items = category.Items
                .Select(x => new CategoryItem
                {
                    Id = x.Id,
                    Name = x.Name,
                    ImageUrl = x.ImageUrl,
                    Price = 7,
                    Measure = "N/A",
                })
                .ToList(),
            SubCategories = category.SubCategories
                .Select(x => new SubCategory
                {
                    Id = x.Id,
                    Name = x.Name,
                    ImageUrl = x.ImageUrl,
                    ItemCount = x.ItemCount
                })
                .ToList()
        };
    }
}

public class GetCategoryRequest : IRequest<GetCategoryResponse>
{
    public required string Id { get; set; }
}

public class GetCategoryResponse
{
    public required string Id { get; set; }

    public required string Name { get; set; }

    public required string ImageUrl { get; set; }

    public required List<CategoryItem> Items { get; set; } = [];

    public required List<SubCategory> SubCategories { get; set; } = [];
}

public class CategoryItem
{
    public required string Id { get; set; }

    public required string Name { get; set; }

    public required string ImageUrl { get; set; }

    public required decimal Price { get; set; }

    public required string Measure { get; set; }

    public decimal? DiscountPrice { get; set; }

    public int? DiscountPercentage { get; set; }
}

public class SubCategory
{
    public required string Id { get; set; }

    public required string Name { get; set; }

    public required string ImageUrl { get; set; }

    public required int ItemCount { get; set; }
}