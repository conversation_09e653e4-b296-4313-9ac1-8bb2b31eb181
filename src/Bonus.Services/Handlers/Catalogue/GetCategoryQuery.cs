using Bonus.Core.Data;
using Bonus.Services.Services;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Bonus.Services.Handlers.Catalogue;

public class GetCategoryQuery(ICatalogueService catalogueService, BonusContext dbContext)
    : IRequestHandler<GetCategoryRequest, GetCategoryResponse>
{
    public async Task<GetCategoryResponse> Handle(GetCategoryRequest request, CancellationToken cancellationToken)
    {
        var category = await catalogueService.GetCategoryByIdAsync(request.Id, cancellationToken);
        var itemIds = category.Items.Select(x => x.Id).ToList();
        
        var productPrices = await dbContext.Products
            .Where(p => itemIds.Contains(p.ItemId))
            .Select(p => new { p.ItemId, p.UnitPrice })
            .ToDictionaryAsync(p => p.ItemId, cancellationToken);

        return new GetCategoryResponse
        {
            Id = category.Id,
            Name = category.Name,
            ImageUrl = category.ImageUrl,
            Items = category.Items
                .Select(x => new CategoryItem
                {
                    Id = x.Id,
                    Name = x.Name,
                    ImageUrl = x.ImageUrl,
                    Price = productPrices.TryGetValue(x.Id, out var priceInfo) ? priceInfo.UnitPrice : 0,
                    Measure = "N/A",
                })
                .ToList(),
            SubCategories = category.SubCategories
                .Select(x => new SubCategory
                {
                    Id = x.Id,
                    Name = x.Name,
                    ImageUrl = x.ImageUrl,
                    ItemCount = x.ItemCount
                })
                .ToList()
        };
    }
}

public class GetCategoryRequest : IRequest<GetCategoryResponse>
{
    public required string Id { get; set; }
}

public class GetCategoryResponse
{
    public required string Id { get; set; }

    public required string Name { get; set; }

    public required string ImageUrl { get; set; }

    public required List<CategoryItem> Items { get; set; } = [];

    public required List<SubCategory> SubCategories { get; set; } = [];
}

public class CategoryItem
{
    public required string Id { get; set; }

    public required string Name { get; set; }

    public required string ImageUrl { get; set; }

    public required decimal Price { get; set; }

    public required string Measure { get; set; }

    public decimal? DiscountPrice { get; set; }

    public int? DiscountPercentage { get; set; }
}

public class SubCategory
{
    public required string Id { get; set; }

    public required string Name { get; set; }

    public required string ImageUrl { get; set; }

    public required int ItemCount { get; set; }
}