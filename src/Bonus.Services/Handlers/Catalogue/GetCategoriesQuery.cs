using Bonus.Services.Services;
using MediatR;

namespace Bonus.Services.Handlers.Catalogue;

public class GetCategoriesQuery(ICatalogueService catalogueService)
    : IRequestHandler<GetCategoriesRequest, GetCategoriesResponse>
{
    public async Task<GetCategoriesResponse> Handle(GetCategoriesRequest request, CancellationToken cancellationToken)
    {
        var categoriesData = await catalogueService.GetCategoriesAsync(cancellationToken);

        var categories = categoriesData.Select(c =>
            new Category
            {
                Id = c.Id,
                Name = c.Name,
                ImageUrl = c.ImageUrl,
                ItemCount = c.ItemCount,
                SubcategoryCount = c.SubcategoryCount
            })
            .ToList();

        return new GetCategoriesResponse
        {
            Categories = categories
        };
    }
}

public class GetCategoriesRequest : IRequest<GetCategoriesResponse>;

public class GetCategoriesResponse
{
    public required List<Category> Categories { get; set; } = [];
}

public class Category
{
    public required string Id { get; set; }

    public required string Name { get; set; }

    public required string ImageUrl { get; set; }

    public int ItemCount { get; set; }

    public int SubcategoryCount { get; set; }
}