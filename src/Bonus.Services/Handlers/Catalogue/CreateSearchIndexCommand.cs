using Bonus.Services.Services;
using MediatR;

namespace Bonus.Services.Handlers.Catalogue;

public class CreateSearchIndexCommand(IProductSearchService searchService) : IRequestHandler<CreateSearchIndexRequest, CreateSearchIndexResponse>
{
    public async Task<CreateSearchIndexResponse> Handle(CreateSearchIndexRequest request, CancellationToken cancellationToken)
    {
        await searchService.IndexAllItemsAsync(cancellationToken);

        return new CreateSearchIndexResponse();
    }
}

public class CreateSearchIndexRequest : IRequest<CreateSearchIndexResponse>;

public class CreateSearchIndexResponse;