using Bonus.Resources;
using Bonus.Services.Services;
using Bonus.Shared.Helpers;
using Bonus.Shared.Types.Common.Exceptions;
using Bonus.Shared.Types.Interfaces;
using MediatR;

namespace Bonus.Services.Handlers.ShoppingList;

public class AddShoppingListItemCommand(IShoppingListService shoppingListService, IBonusUser bonusUser)
    : IRequestHandler<AddShoppingListItemRequest, ResultShoppingList>
{
    public async Task<ResultShoppingList> Handle(AddShoppingListItemRequest request, CancellationToken cancellationToken)
    {
        if (bonusUser.CardId.HasValue() is false)
        {
            throw new BonusException(nameof(Translations.UserNotAuthenticated));
        }

        if (request.ShoppingListId.HasValue() is false)
        {
            throw new BonusException(nameof(Translations.ShoppingList_IdRequired));
        }

        if (request.Name.HasValue() is false)
        {
            throw new BonusException(nameof(Translations.ShoppingList_ItemNameRequired));
        }

        return await shoppingListService.AddShoppingListItem(request.ShoppingListId, new CreateShoppingListItem(request.Name, request.ItemId, request.Quantity), cancellationToken);
    }
}

public class AddShoppingListItemRequest : IRequest<ResultShoppingList>
{
    public required string ShoppingListId { get; set; }

    public required string Name { get; set; }

    public string? ItemId { get; set; }

    public required decimal Quantity { get; set; }
}