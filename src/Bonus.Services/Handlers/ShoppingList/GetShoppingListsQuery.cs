using Bonus.Resources;
using Bonus.Services.Services;
using Bonus.Shared.Helpers;
using Bonus.Shared.Types.Common.Exceptions;
using Bonus.Shared.Types.Interfaces;
using MediatR;

namespace Bonus.Services.Handlers.ShoppingList;

public class GetShoppingListsQuery(IShoppingListService shoppingListService, IBonusUser bonusUser)
    : IRequestHandler<GetShoppingListsRequest, GetShoppingListsResponse>
{
    public async Task<GetShoppingListsResponse> Handle(GetShoppingListsRequest request, CancellationToken cancellationToken)
    {
        if (bonusUser.CardId.HasValue() is false)
        {
            throw new BonusException(nameof(Translations.UserNotAuthenticated));
        }

        var oneLists = await shoppingListService.GetShoppingLists(cancellationToken);

        return new GetShoppingListsResponse
        {
            ShoppingLists = oneLists
        };
    }
}

public class GetShoppingListsRequest : IRequest<GetShoppingListsResponse>;

public class GetShoppingListsResponse
{
    public required List<ResultShoppingList> ShoppingLists { get; set; } = [];
}