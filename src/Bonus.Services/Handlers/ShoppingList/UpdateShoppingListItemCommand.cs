using Bonus.Resources;
using Bonus.Services.Services;
using Bonus.Shared.Helpers;
using Bonus.Shared.Types.Common.Exceptions;
using Bonus.Shared.Types.Interfaces;
using MediatR;

namespace Bonus.Services.Handlers.ShoppingList;

public class UpdateShoppingListItemCommand(IShoppingListService shoppingListService, IBonusUser bonusUser)
    : IRequestHandler<UpdateShoppingListItemRequest, ResultShoppingList>
{
    public async Task<ResultShoppingList> Handle(UpdateShoppingListItemRequest request, CancellationToken cancellationToken)
    {
        if (bonusUser.CardId.HasValue() is false)
        {
            throw new BonusException(nameof(Translations.UserNotAuthenticated));
        }

        if (request.ShoppingListId.HasValue() is false)
        {
            throw new BonusException(nameof(Translations.ShoppingList_IdRequired));
        }

        if (request.Id.HasValue() is false)
        {
            throw new BonusException(nameof(Translations.ShoppingList_ItemIdRequired));
        }

        var serviceUpdateRequest = new UpdateShoppingListItem(request.Name, request.ItemId, request.Quantity);
        return await shoppingListService.UpdateShoppingListItem(request.ShoppingListId, request.Id, serviceUpdateRequest, cancellationToken);
    }
}

public class UpdateShoppingListItemRequest : IRequest<ResultShoppingList>
{
    public required string ShoppingListId { get; set; }

    public required string Id { get; set; }

    public required string Name { get; set; }

    public string? ItemId { get; set; }

    public string? Description { get; set; }

    public decimal? Price { get; set; }

    public decimal? Quantity { get; set; }

    public string? Measure { get; set; }
}