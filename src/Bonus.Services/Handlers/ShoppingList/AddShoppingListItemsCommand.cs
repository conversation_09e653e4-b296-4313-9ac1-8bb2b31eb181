using Bonus.Resources;
using Bonus.Services.Services;
using Bonus.Shared.Helpers;
using Bonus.Shared.Types.Common.Exceptions;
using Bonus.Shared.Types.Interfaces;
using MediatR;

namespace Bonus.Services.Handlers.ShoppingList;

public class AddShoppingListItemsCommand(IShoppingListService shoppingListService, IBonusUser bonusUser)
    : IRequestHandler<AddShoppingListItemsRequest, ResultShoppingList>
{
    public async Task<ResultShoppingList> Handle(AddShoppingListItemsRequest request, CancellationToken cancellationToken)
    {
        if (bonusUser.CardId.HasValue() is false)
        {
            throw new BonusException(nameof(Translations.UserNotAuthenticated));
        }

        if (request.ShoppingListId.HasValue() is false)
        {
            throw new BonusException(nameof(Translations.ShoppingList_IdRequired));
        }

        if (request.Items.Any(x => x.Name.HasValue() is false))
        {
            throw new BonusException(nameof(Translations.ShoppingList_ItemNameRequired));
        }
        
        var items = request.Items.Select(x => new CreateShoppingListItem(x.Name, x.ItemId, x.Quantity)).ToList();

        return await shoppingListService.AddShoppingListItems(request.ShoppingListId, items, cancellationToken);
    }
}

public class AddShoppingListItemsRequest : IRequest<ResultShoppingList>
{
    public required string ShoppingListId { get; set; }
    
    public List<AddShoppingListItemsRequestItem> Items { get; set; } = [];
}

public class AddShoppingListItemsRequestItem
{
    public required string Name { get; set; }

    public string? ItemId { get; set; }

    public required decimal Quantity { get; set; }
}

