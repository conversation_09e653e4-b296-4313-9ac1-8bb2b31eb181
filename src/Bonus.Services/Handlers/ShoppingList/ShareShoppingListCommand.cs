using Bonus.Adapters.LSRetail.Enums;
using Bonus.Resources;
using Bonus.Services.Services;
using Bonus.Shared.Helpers;
using Bonus.Shared.Types.Common.Exceptions;
using Bonus.Shared.Types.Interfaces;
using MediatR;

namespace Bonus.Services.Handlers.ShoppingList;

public class ShareShoppingListCommand(IShoppingListService shoppingListService, IBonusUser bonusUser)
    : IRequestHandler<ShareShoppingListRequest, ResultShoppingList>
{
    public async Task<ResultShoppingList> Handle(ShareShoppingListRequest request, CancellationToken cancellationToken)
    {
        if (bonusUser.CardId.HasValue() is false)
        {
            throw new BonusException(nameof(Translations.UserNotAuthenticated));
        }

        if (request.ShoppingListId.HasValue() is false)
        {
            throw new BonusException(nameof(Translations.ShoppingList_IdRequired));
        }
        
        if (request.ContactMethod is not ContactSearchType.Email and not ContactSearchType.PhoneNumber and not ContactSearchType.CardId)
        {
            throw new BonusException(nameof(Translations.ShoppingList_InvalidContactMethod));
        }

        if (request.ContactValue.HasValue() is false)
        {
            throw new BonusException(nameof(Translations.ShoppingList_ContactValueRequired));
        }

        return await shoppingListService.ShareShoppingList(request.ShoppingListId, request.ContactMethod, request.ContactValue, cancellationToken);
    }
}

public class ShareShoppingListRequest : IRequest<ResultShoppingList>
{
    public required string ShoppingListId { get; set; }

    public required ContactSearchType ContactMethod { get; set; }

    public required string ContactValue { get; set; }
}