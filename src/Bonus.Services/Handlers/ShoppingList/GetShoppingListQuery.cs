using Bonus.Resources;
using Bonus.Services.Services;
using Bonus.Shared.Helpers;
using Bonus.Shared.Types.Common.Exceptions;
using Bonus.Shared.Types.Interfaces;
using MediatR;

namespace Bonus.Services.Handlers.ShoppingList;

public class GetShoppingListQuery(IShoppingListService shoppingListService, IBonusUser bonusUser)
    : IRequestHandler<GetShoppingListRequest, ResultShoppingList>
{
    public async Task<ResultShoppingList> Handle(GetShoppingListRequest request, CancellationToken cancellationToken)
    {
        if (bonusUser.CardId.HasValue() is false)
        {
            throw new BonusException(nameof(Translations.UserNotAuthenticated));
        }

        if (request.Id.HasValue() is false)
        {
            throw new BonusException(nameof(Translations.ShoppingList_IdRequired));
        }

        return await shoppingListService.GetShoppingList(request.Id, cancellationToken);
    }
}

public class GetShoppingListRequest : IRequest<ResultShoppingList>
{
    public required string Id { get; set; }
}