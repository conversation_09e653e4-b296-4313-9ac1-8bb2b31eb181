using Bonus.Resources;
using Bonus.Services.Services;
using Bonus.Shared.Helpers;
using Bonus.Shared.Types.Common.Exceptions;
using Bonus.Shared.Types.Interfaces;
using MediatR;

namespace Bonus.Services.Handlers.ShoppingList;

public class UpdateShoppingListCommand(IShoppingListService shoppingListService, IBonusUser bonusUser)
    : IRequestHandler<UpdateShoppingListRequest, ResultShoppingList>
{
    public async Task<ResultShoppingList> Handle(UpdateShoppingListRequest request, CancellationToken cancellationToken)
    {
        if (bonusUser.CardId.HasValue() is false)
        {
            throw new BonusException(nameof(Translations.UserNotAuthenticated));
        }

        if (request.Id.HasValue() is false)
        {
            throw new BonusException(nameof(Translations.ShoppingList_IdRequired));
        }

        if (request.Name.HasValue() is false)
        {
            throw new BonusException(nameof(Translations.ShoppingList_NameRequired));
        }

        return await shoppingListService.UpdateShoppingList(request.Id, request.Name, cancellationToken);
    }
}

public class UpdateShoppingListRequest : IRequest<ResultShoppingList>
{
    public required string Id { get; set; }

    public required string Name { get; set; }
}