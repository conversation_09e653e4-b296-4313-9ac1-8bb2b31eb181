using Bonus.Resources;
using Bonus.Services.Services;
using Bonus.Shared.Helpers;
using Bonus.Shared.Types.Common.Exceptions;
using Bonus.Shared.Types.Interfaces;
using MediatR;

namespace Bonus.Services.Handlers.ShoppingList;

public class RemoveShoppingListShareCommand(IShoppingListService shoppingListService, IBonusUser bonusUser)
    : IRequestHandler<RemoveShoppingListShareRequest, ResultShoppingList>
{
    public async Task<ResultShoppingList> Handle(RemoveShoppingListShareRequest request, CancellationToken cancellationToken)
    {
        if (bonusUser.CardId.HasValue() is false)
        {
            throw new BonusException(nameof(Translations.UserNotAuthenticated));
        }

        if (request.Id.HasValue() is false)
        {
            throw new BonusException(nameof(Translations.ShoppingList_IdRequired));
        }

        if (request.ShareId.HasValue() is false)
        {
            throw new BonusException(nameof(Translations.ShoppingList_ShareIdRequired));
        }

        return await shoppingListService.RemoveShoppingListShare(request.Id, request.ShareId, cancellationToken);
    }
}

public class RemoveShoppingListShareRequest : IRequest<ResultShoppingList>
{
    public required string Id { get; set; }
    
    public required string ShareId { get; set; }
}