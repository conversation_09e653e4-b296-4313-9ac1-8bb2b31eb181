using Bonus.Resources;
using Bonus.Services.Services;
using Bonus.Shared.Helpers;
using Bonus.Shared.Types.Common.Exceptions;
using Bonus.Shared.Types.Interfaces;
using MediatR;

namespace Bonus.Services.Handlers.ShoppingList;

public class RemoveShoppingListCommand(IShoppingListService shoppingListService, IBonusUser bonusUser) : IRequestHandler<RemoveShoppingListRequest, RemoveShoppingListResponse>
{
    public async Task<RemoveShoppingListResponse> Handle(RemoveShoppingListRequest request, CancellationToken cancellationToken)
    {
        if (bonusUser.CardId.HasValue() is false)
        {
            throw new BonusException(nameof(Translations.UserNotAuthenticated));
        }

        if (request.Id.HasValue() is false)
        {
            throw new BonusException(nameof(Translations.ShoppingList_IdRequired));
        }

        await shoppingListService.RemoveShoppingList(request.Id, cancellationToken);

        return new RemoveShoppingListResponse();
    }
}

public class RemoveShoppingListRequest : IRequest<RemoveShoppingListResponse>
{
    public required string Id { get; set; }
}

public class RemoveShoppingListResponse;