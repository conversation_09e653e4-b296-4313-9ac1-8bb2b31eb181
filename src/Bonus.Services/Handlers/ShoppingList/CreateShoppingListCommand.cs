using Bonus.Resources;
using Bonus.Services.Services;
using Bonus.Shared.Helpers;
using Bonus.Shared.Types.Common.Exceptions;
using Bonus.Shared.Types.Interfaces;
using MediatR;

namespace Bonus.Services.Handlers.ShoppingList;

public class CreateShoppingListCommand(IShoppingListService shoppingListService, IBonusUser bonusUser)
    : IRequestHandler<CreateShoppingListRequest, ResultShoppingList>
{
    public async Task<ResultShoppingList> Handle(CreateShoppingListRequest request, CancellationToken cancellationToken)
    {
        if (bonusUser.CardId.HasValue() is false)
        {
            throw new BonusException(nameof(Translations.UserNotAuthenticated));
        }

        if (request.Name.HasValue() is false)
        {
            throw new BonusException(nameof(Translations.ShoppingList_NameRequired));
        }
        
        var items = request.Items.Select(x => new CreateShoppingListItem(x.Name, x.ItemId, x.Quantity)).ToList();

        return await shoppingListService.CreateShoppingList(request.Name, items, cancellationToken);
    }
}

public class CreateShoppingListRequest : IRequest<ResultShoppingList>
{
    public required string Name { get; set; }

    public List<CreateShoppingListRequestItem> Items { get; set; } = [];
}

public class CreateShoppingListRequestItem
{
    public required string Name { get; set; }

    public string? ItemId { get; set; }

    public required int Quantity { get; set; }
}