using Bonus.Resources;
using Bonus.Services.Services;
using Bonus.Shared.Helpers;
using Bonus.Shared.Types.Common.Exceptions;
using Bonus.Shared.Types.Interfaces;
using MediatR;

namespace Bonus.Services.Handlers.ShoppingList;

public class RemoveShoppingListItemsCommand(IShoppingListService shoppingListService, IBonusUser bonusUser)
    : IRequestHandler<RemoveShoppingListItemsRequest, ResultShoppingList>
{
    public async Task<ResultShoppingList> Handle(RemoveShoppingListItemsRequest request, CancellationToken cancellationToken)
    {
        if (bonusUser.CardId.HasValue() is false)
        {
            throw new BonusException(nameof(Translations.UserNotAuthenticated));
        }

        if (request.ShoppingListId.HasValue() is false)
        {
            throw new BonusException(nameof(Translations.ShoppingList_IdRequired));
        }

        if (request.Ids.Any() is false)
        {
            throw new BonusException(nameof(Translations.ShoppingList_ItemIdRequired));
        }

        return await shoppingListService.RemoveShoppingListItems(request.ShoppingListId, request.Ids, cancellationToken);
    }
}

public class RemoveShoppingListItemsRequest : IRequest<ResultShoppingList>
{
    public required string ShoppingListId { get; set; }

    public required List<string> Ids { get; set; }
}