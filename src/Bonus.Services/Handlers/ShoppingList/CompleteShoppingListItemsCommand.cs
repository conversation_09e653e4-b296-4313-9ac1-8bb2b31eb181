using Bonus.Resources;
using Bonus.Services.Services;
using Bonus.Shared.Helpers;
using Bonus.Shared.Types.Common.Exceptions;
using Bonus.Shared.Types.Interfaces;
using MediatR;

namespace Bonus.Services.Handlers.ShoppingList;

public class CompleteShoppingListItemsCommand(IShoppingListService shoppingListService, IBonusUser bonusUser)
    : IRequestHandler<CompleteShoppingListItemsRequest, ResultShoppingList>
{
    public async Task<ResultShoppingList> Handle(CompleteShoppingListItemsRequest request, CancellationToken cancellationToken)
    {
        if (bonusUser.CardId.HasValue() is false)
        {
            throw new BonusException(nameof(Translations.UserNotAuthenticated));
        }

        if (request.ShoppingListId.HasValue() is false)
        {
            throw new BonusException(nameof(Translations.ShoppingList_IdRequired));
        }

        if (request.Ids.Any() is false)
        {
            throw new BonusException(nameof(Translations.ShoppingList_ItemIdRequired));
        }

        return await shoppingListService.CompleteShoppingListItems(request.ShoppingListId, request.Ids, request.IsCompleted, cancellationToken);
    }
}

public class CompleteShoppingListItemsRequest : IRequest<ResultShoppingList>
{
    public required string ShoppingListId { get; set; }

    public required List<string> Ids { get; set; }

    public required bool IsCompleted { get; set; }
}