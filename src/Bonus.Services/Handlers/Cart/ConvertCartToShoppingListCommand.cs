using Bonus.Adapters.EasyShop;
using Bonus.Adapters.LSRetail;
using Bonus.Adapters.LSRetail.Enums;
using Bonus.Adapters.LSRetail.Models.Baskets;
using Bonus.Adapters.LSRetail.Requests;
using Bonus.Core.Services;
using Bonus.Resources;
using Bonus.Shared.Types.Common.Exceptions;
using Bonus.Shared.Types.Interfaces;
using MediatR;

namespace Bonus.Services.Handlers.Cart;

public class ConvertCartToShoppingListCommand(IBonusUser bonusUser, IEasyShopAdapter easyShopAdapter, ILSRetailAdapter lsRetailAdapter, ISystemTime systemTime) : IRequestHandler<ConvertCartToShoppingListRequest, ConvertCartToShoppingListResponse>
{
    public async Task<ConvertCartToShoppingListResponse> Handle(ConvertCartToShoppingListRequest request, CancellationToken cancellationToken)
    {
        var easyShopRequest = new EasyShopGetShoppingTripRequest
        {
            StoreId = request.StoreId,
            CartId = request.CartId,
            ShopperIdentifier = bonusUser.CardId,
            AcceptLanguage = bonusUser.EasyShopCulture,
            DeviceId = bonusUser.DeviceId,
            CorrelationId = Guid.NewGuid().ToString()
        };

        var shoppingTripResult = await easyShopAdapter.GetShoppingTrip(easyShopRequest, cancellationToken);

        if (shoppingTripResult.Success is false)
        {
            throw new BonusException(nameof(Translations.Cart_FailedToGetCart_EasyShop), shoppingTripResult.Error?.Message);
        }

        var oneList = new OneList
        {
            Id = null!,
            CardId = bonusUser.CardId,
            Description = request.ShoppingListName,
            ListType = ListType.Wish,
            Items = []
        };
        
        foreach (var item in shoppingTripResult.Response?.Cart.Items ?? [])
        {
            oneList.Items.Add(new OneListItem
            {
                Id = null!,
                ItemId = item.Id,
                ItemDescription = item.Article.Description,
                Quantity = item.Quantity,
                IsManualItem = false,
                CreateDate = systemTime.UtcNow,
            });
        }
        
        var saveRequest = new LSSaveOneListRequest
        {
            OneList = oneList,
        };

        var saveResponse = await lsRetailAdapter.SaveOneList(saveRequest, cancellationToken);

        if (saveResponse.Response?.OneList is null)
        {
            throw new BonusException(nameof(Translations.ShoppingList_CreatedFailed));
        }

        return new ConvertCartToShoppingListResponse
        {
            ShoppingListId = saveResponse.Response!.OneList.Id,
            ShoppingListName = request.ShoppingListName,
            ItemCount = saveResponse.Response!.OneList.Items.Count,
        };
    }
}

public class ConvertCartToShoppingListRequest : IRequest<ConvertCartToShoppingListResponse>
{
    public required string CartId { get; set; }
    
    public required string StoreId { get; set; }
    
    public required string ShoppingListName { get; set; }
}

public class ConvertCartToShoppingListResponse
{
    public required string ShoppingListId { get; set; }

    public required string ShoppingListName { get; set; }

    public required int ItemCount { get; set; }
}