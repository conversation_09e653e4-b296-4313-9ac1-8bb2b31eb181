using Bonus.Adapters.EasyShop;
using Bonus.Adapters.EasyShop.Enums;
using Bonus.Adapters.EasyShop.Models;
using Bonus.Resources;
using Bonus.Shared.Helpers;
using Bonus.Shared.Types.Common.Exceptions;
using Bonus.Shared.Types.Interfaces;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Bonus.Services.Handlers.Cart;

public class GetCartStatusQuery(IBonusUser bonusUser, IEasyShopAdapter easyShopAdapter, ILogger<GetCartStatusQuery> logger)
    : IRequestHandler<GetCartStatusRequest, GetCartStatusResponse>
{
    public async Task<GetCartStatusResponse> Handle(GetCartStatusRequest request, CancellationToken cancellationToken)
    {
        var easyShopRequest = new EasyShopGetEndShoppingTripStateRequest
        {
            StoreId = request.StoreId,
            CartId = request.CartId.HasValue() ? request.CartId : "0",
            ShopperIdentifier = bonusUser.CardId,
            AcceptLanguage = bonusUser.EasyShopCulture,
            DeviceId = bonusUser.DeviceId,
            CorrelationId = Guid.NewGuid().ToString()
        };

        var result = await easyShopAdapter.GetEndShoppingTripState(easyShopRequest, cancellationToken);

        if (result.Success is false)
        {
            throw new BonusException(nameof(Translations.Cart_FailedToGetCartStatus_EasyShop), result.Error?.Message);
        }

        var status = MapShoppingTripStatus(result.Response!);
        
        logger.LogInformation("Received cart status for cart {CartId} in store {StoreId}: Shopping Trip Status: {Status}, Payment Allowed Status: {PaymentStatus}, Payment Blocked Reason: {PaymentBlockedReason}. Calculated: {MobileStatus}",
            result.Response!.Id, request.StoreId, result.Response?.ShoppingTripStatus.ToString(), result.Response!.PaymentAllowedStatus.ToString(), result.Response!.PaymentBlockedReason.ToString(), status);

        return new GetCartStatusResponse
        {
            CartId = result.Response!.Id ?? request.CartId!,
            Status = status
        };
    }

    private static string MapShoppingTripStatus(ESEndShoppingTripStateDto response) => response.ShoppingTripStatus switch
    {
        ShoppingTripStatus.WaitingForPayment when response.PaymentAllowedStatus is PaymentAllowedStatus.Allowed => "please_pay",
        ShoppingTripStatus.WaitingForPayment when response.PaymentAllowedStatus is PaymentAllowedStatus.Blocked => "control_failed",
        ShoppingTripStatus.WaitingForPayment when response.PaymentAllowedStatus is PaymentAllowedStatus.Pending or PaymentAllowedStatus.PendingExpired => "staff_check",
        ShoppingTripStatus.Completed => "payment_success",
        ShoppingTripStatus.Cancelled or ShoppingTripStatus.NoPayment or ShoppingTripStatus.Abandoned => "payment_failed",
        ShoppingTripStatus.Active => "active",
        _ => "unknown"
    };
}

public class GetCartStatusRequest : IRequest<GetCartStatusResponse>
{
    public required string? CartId { get; set; }
    
    public required string StoreId { get; set; }
}

public class GetCartStatusResponse
{
    public required string CartId { get; set; }

    public required string Status { get; set; }
}