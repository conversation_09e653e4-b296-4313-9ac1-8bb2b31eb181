using Bonus.Adapters.EasyShop;
using Bonus.Adapters.EasyShop.Models;
using Bonus.Resources;
using Bonus.Shared.Types.Common.Exceptions;
using Bonus.Shared.Types.Interfaces;
using MediatR;

namespace Bonus.Services.Handlers.Cart;

public class UpdateCartItemCommand(IBonusUser bonusUser, IEasyShopAdapter easyShopAdapter) : IRequestHandler<UpdateCartItemRequest, UpdateCartItemResponse>
{
    public async Task<UpdateCartItemResponse> Handle(UpdateCartItemRequest request, CancellationToken cancellationToken)
    {
        var easyShopRequest = new EasyShopChangeTripItemQuantityRequest
        {
            StoreId = request.StoreId,
            CartId = request.CartId,
            ItemId = request.ItemId,
            Body = new ESChangeTripItemQuantityDto
            {
                Quantity = request.Quantity
            },
            ShopperIdentifier = bonusUser.CardId,
            AcceptLanguage = bonusUser.EasyShopCulture,
            DeviceId = bonusUser.DeviceId,
            CorrelationId = Guid.NewGuid().ToString()
        };

        var result = await easyShopAdapter.ChangeTripItemQuantity(easyShopRequest, cancellationToken);

        if (result.Success is false)
        {
            throw new BonusException(nameof(Translations.Cart_UpdateItemFailed_EasyShop), result.Error?.Message);
        }

        var updatedItem = result.Response!.Items.First(x => x.Id == request.ItemId);

        return new UpdateCartItemResponse
        {
            Id = updatedItem.Id,
            ItemId = updatedItem.Article.Barcode.Data,
            Name = updatedItem.Article.Description,
            ImageUrl = "",
            Price = updatedItem.Article.Price,
            Measure = updatedItem.Article.UnitOfMeasure.ToString(),
            Quantity = updatedItem.Quantity,
            DiscountPrice = null,
            DiscountPercentage = null,
            TotalPrice = updatedItem.Total,
            CartTotalPrice = result.Response!.Total,
            CartTotalItems = result.Response!.Items.Count
        };
    }
}

public class UpdateCartItemRequest : IRequest<UpdateCartItemResponse>
{
    public required string CartId { get; set; }
    
    public required string StoreId { get; set; }
    
    public required string ItemId { get; set; }
    
    public required decimal Quantity { get; set; }
}

public class UpdateCartItemResponse
{
    public required string Id { get; set; }
    
    public required string ItemId { get; set; }
    
    public required string Name { get; set; }
    
    public required string ImageUrl { get; set; }
    
    public required decimal Price { get; set; }
    
    public required string Measure { get; set; }
    
    public required decimal Quantity { get; set; }
    
    public required decimal? DiscountPrice { get; set; }
    
    public required int? DiscountPercentage { get; set; }
    
    public required decimal TotalPrice { get; set; }
    
    public required decimal CartTotalPrice { get; set; }
    
    public required int CartTotalItems { get; set; }
}