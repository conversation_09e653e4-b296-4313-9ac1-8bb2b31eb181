using Bonus.Adapters.EasyShop;
using Bonus.Resources;
using Bonus.Shared.Types.Common.Exceptions;
using Bonus.Shared.Types.Interfaces;
using MediatR;

namespace Bonus.Services.Handlers.Cart;

public class RemoveCartItemCommand(IBonusUser bonusUser, IEasyShopAdapter easyShopAdapter) : IRequestHandler<RemoveCartItemRequest, RemoveCartItemResponse>
{
    public async Task<RemoveCartItemResponse> Handle(RemoveCartItemRequest request, CancellationToken cancellationToken)
    {
        var easyShopRequest = new EasyShopRemoveTripItemRequest
        {
            StoreId = request.StoreId,
            CartId = request.CartId,
            ItemId = request.ItemId,
            ShopperIdentifier = bonusUser.CardId,
            AcceptLanguage = bonusUser.EasyShopCulture,
            DeviceId = bonusUser.DeviceId,
            CorrelationId = Guid.NewGuid().ToString()
        };

        var result = await easyShopAdapter.RemoveTripItem(easyShopRequest, cancellationToken);

        if (result.Success is false)
        {
            throw new BonusException(nameof(Translations.Cart_RemoveItemFailed_EasyShop), result.Error?.Message);
        }

        return new RemoveCartItemResponse
        {
            ItemId = request.ItemId,
            CartTotalPrice = result.Response!.Total,
            CartTotalItems = result.Response!.Items.Count // TODO: Individual count or sum of quantities?
        };
    }
}

public class RemoveCartItemRequest : IRequest<RemoveCartItemResponse>
{
    public required string CartId { get; set; }
    
    public required string StoreId { get; set; }
    
    public required string ItemId { get; set; }
}

public class RemoveCartItemResponse
{
    public required string ItemId { get; set; }

    public required decimal CartTotalPrice { get; set; }

    public required int CartTotalItems { get; set; }
}