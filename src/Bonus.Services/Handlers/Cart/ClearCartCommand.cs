using Bonus.Adapters.EasyShop;
using Bonus.Resources;
using Bonus.Shared.Types.Common.Exceptions;
using Bonus.Shared.Types.Interfaces;
using MediatR;

namespace Bonus.Services.Handlers.Cart;

public class ClearCartCommand(IBonusUser bonusUser, IEasyShopAdapter easyShopAdapter)
    : IRequestHandler<ClearCartRequest, ClearCartResponse>
{
    public async Task<ClearCartResponse> Handle(ClearCartRequest request, CancellationToken cancellationToken)
    {
        var easyShopRequest = new EasyShopCancelShoppingTripRequest
        {
            StoreId = request.StoreId,
            CartId = request.CartId,
            ShopperIdentifier = bonusUser.CardId,
            AcceptLanguage = bonusUser.EasyShopCulture,
            DeviceId = bonusUser.DeviceId,
            CorrelationId = Guid.NewGuid().ToString()
        };

        var result = await easyShopAdapter.CancelShoppingTrip(easyShopRequest, cancellationToken);

        if (result.Success is false)
        {
            throw new BonusException(nameof(Translations.Cart_FailedToClearCart_EasyShop), result.Error?.Message);
        }

        return new ClearCartResponse
        {
            Success = true
        };
    }
}

public class ClearCartRequest : IRequest<ClearCartResponse>
{
    public required string CartId { get; set; }
    
    public required string StoreId { get; set; }
}

public class ClearCartResponse
{
    public required bool Success { get; set; }
}