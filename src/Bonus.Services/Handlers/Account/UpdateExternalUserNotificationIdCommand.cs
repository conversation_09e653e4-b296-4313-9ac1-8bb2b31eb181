using Bonus.Core.Data;
using Bonus.Shared.Types.Interfaces;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Bonus.Services.Handlers.Account;

public record UpdateExternalUserNotificationIdRequest(string ExternalUserNotificationId) : IRequest;

public class UpdateExternalUserNotificationIdCommand(BonusContext context, ILogger<UpdateExternalUserNotificationIdCommand> logger, IBonusUser bonusUser) : IRequestHandler<UpdateExternalUserNotificationIdRequest>
{
    public async Task Handle(UpdateExternalUserNotificationIdRequest request, CancellationToken cancellationToken)
    {
        var user = await context.Users
            .Where(u => u.Id == bonusUser.UserId)
            .FirstOrDefaultAsync(cancellationToken);

        if (user == null)
        {
            throw new InvalidOperationException($"Authenticated user not found in database.");
        }

        var oldValue = user.UserExternalNotificationId;
        user.UserExternalNotificationId = request.ExternalUserNotificationId;

        await context.SaveChangesAsync(cancellationToken);

        logger.LogInformation("Updating external user notification ID for user {UserId}. Old value: {OldValue}, New value: {NewValue}", bonusUser.UserId, oldValue ?? "null", request.ExternalUserNotificationId);
    }
}
