using Bonus.Adapters.LSRetail;
using Bonus.Adapters.LSRetail.Requests;
using Bonus.Resources;
using Bonus.Shared.Helpers;
using Bonus.Shared.Types.Common.Exceptions;
using Bonus.Shared.Types.Interfaces;
using MediatR;

namespace Bonus.Services.Handlers.Account;

public class UpdateNameCommand(ILSRetailAdapter lsRetailAdapter, IBonusUser bonusUser)
    : IRequestHandler<UpdateNameRequest, UpdateNameResponse>
{
    public async Task<UpdateNameResponse> Handle(UpdateNameRequest request, CancellationToken cancellationToken)
    {
        if (bonusUser.CardId.HasValue() is false)
        {
            throw new BonusException(nameof(Translations.UserNotAuthenticated));
        }

        var contactRequest = new LSGetContactGetByCardIdRequest
        {
            CardId = bonusUser.CardId,
            NumberOfTransactionsReturned = 0,
        };

        var contactResponse = await lsRetailAdapter.GetContactByCardId(contactRequest, cancellationToken);

        if (contactResponse.Response?.Contact is null)
        {
            throw new BonusException(nameof(Translations.Account_ContactNotFound));
        }
        
        var contact = contactResponse.Response!.Contact;

        contact.FirstName = request.FirstName;
        contact.LastName = request.LastName;
        contact.Name = $"{request.FirstName} {request.LastName}";

        var updateRequest = new LSContactUpdateRequest
        {
            Contact = contact,
            GetContact = true
        };

        var updateResponse = await lsRetailAdapter.UpdateContact(updateRequest, cancellationToken);

        if (updateResponse.Response?.Contact is null)
        {
            throw new BonusException(nameof(Translations.Account_UpdatedNameFailed));
        }

        return new UpdateNameResponse
        {
            FirstName = updateResponse.Response!.Contact.FirstName!,
            LastName = updateResponse.Response!.Contact.LastName!
        };
    }
}

public class UpdateNameRequest : IRequest<UpdateNameResponse>
{
    public required string FirstName { get; set; }

    public required string LastName { get; set; }
}

public class UpdateNameResponse
{
    public required string FirstName { get; set; }

    public required string LastName { get; set; }
}