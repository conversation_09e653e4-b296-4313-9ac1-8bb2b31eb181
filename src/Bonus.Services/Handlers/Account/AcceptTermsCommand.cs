using Bonus.Core.Data;
using Bonus.Resources;
using Bonus.Services.Services;
using Bonus.Shared.Helpers;
using Bonus.Shared.Types.Common.Exceptions;
using Bonus.Shared.Types.Interfaces;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Bonus.Services.Handlers.Account;

public class AcceptTermsCommand(BonusContext bonusContext, IHagarIdAuthenticationService hagarIdAuthenticationService, IBonusUser bonusUser)
    : IRequestHandler<AcceptTermsRequest, AcceptTermsResponse>
{
    public async Task<AcceptTermsResponse> Handle(AcceptTermsRequest request, CancellationToken cancellationToken)
    {
        var userSsn = await bonusContext.Users
            .Where(x => x.Id == bonusUser.UserId)
            .Select(x => x.Ssn)
            .FirstAsync(cancellationToken);

        if (userSsn.HasValue() is false)
        {
            throw new BonusException(nameof(Translations.Account_AcceptTermsFailed));
        }

        var response = await hagarIdAuthenticationService.AcceptTerms(userSsn!, bonusUser.IpAddress, cancellationToken);

        if (response == null || response.SessionId.HasValue() is false)
        {
            throw new BonusException(nameof(Translations.Account_AcceptTermsFailed));
        }

        return new();
    }
}

public class AcceptTermsRequest : IRequest<AcceptTermsResponse>;

public class AcceptTermsResponse;