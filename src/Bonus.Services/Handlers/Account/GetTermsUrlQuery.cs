using Bonus.Shared.Configuration.Settings;
using MediatR;
using Microsoft.Extensions.Options;

namespace Bonus.Services.Handlers.Account;

public class GetTermsUrlQuery(IOptionsSnapshot<EnvironmentSettings> options) : IRequestHandler<GetTermsUrlRequest, GetTermsUrlResponse>
{
    public async Task<GetTermsUrlResponse> Handle(GetTermsUrlRequest request, CancellationToken cancellationToken)
    {
        return new GetTermsUrlResponse
        {
            TermsUrl = options.Value.TermsUrl
        };
    }
}

public class GetTermsUrlRequest : IRequest<GetTermsUrlResponse>;

public class GetTermsUrlResponse
{
    public required string TermsUrl { get; set; }
}