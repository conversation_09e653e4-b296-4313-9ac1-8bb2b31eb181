using Bonus.Adapters.LSRetail;
using Bonus.Adapters.LSRetail.Requests;
using Bonus.Resources;
using Bonus.Shared.Helpers;
using Bonus.Shared.Types.Common.Exceptions;
using Bonus.Shared.Types.Interfaces;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Bonus.Services.Handlers.Account;

public class DeleteAccountCommand(ILSRetailAdapter lsRetailAdapter, IBonusUser bonusUser, ILogger<DeleteAccountCommand> logger)
    : IRequestHandler<DeleteAccountRequest, DeleteAccountResponse>
{
    public async Task<DeleteAccountResponse> Handle(DeleteAccountRequest request, CancellationToken cancellationToken)
    {
        if (bonusUser.CardId.HasValue() is false)
        {
            throw new BonusException(nameof(Translations.UserNotAuthenticated));
        }

        logger.LogInformation("User {UserId} with CardId {CardId} has requested account deletion. Reason: {DeleteReason}", bonusUser.UserId, bonusUser.CardId, request.DeleteReason);

        var contactRequest = new LSGetContactGetByCardIdRequest
        {
            CardId = bonusUser.CardId,
            NumberOfTransactionsReturned = 0,
        };

        var contactResponse = await lsRetailAdapter.GetContactByCardId(contactRequest, cancellationToken);

        if (contactResponse.Response?.Contact?.Account?.Id.HasValue() is false)
        {
            throw new BonusException(nameof(Translations.Account_ContactNotFound));
        }

        var blockRequest = new LSContactBlockRequest
        {
            CardId = bonusUser.CardId,
            AccountId = contactResponse.Response!.Contact!.Account!.Id
        };

        var blockResponse = await lsRetailAdapter.BlockContact(blockRequest, cancellationToken);

        if (blockResponse.Success is false)
        {
            throw new BonusException(nameof(Translations.Account_DeleteFailed));
        }

        return new();
    }
}

public class DeleteAccountRequest : IRequest<DeleteAccountResponse>
{
    public string? DeleteReason { get; set; }
}

public class DeleteAccountResponse;