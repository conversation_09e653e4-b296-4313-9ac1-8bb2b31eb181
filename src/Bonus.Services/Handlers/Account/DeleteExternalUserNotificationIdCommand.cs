using Bonus.Core.Data;
using Bonus.Shared.Types.Interfaces;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Bonus.Services.Handlers.Account;

public record DeleteExternalUserNotificationIdRequest() : IRequest;

public class DeleteExternalUserNotificationIdCommand(BonusContext context, ILogger<DeleteExternalUserNotificationIdCommand> logger, IBonusUser bonusUser) : IRequestHandler<DeleteExternalUserNotificationIdRequest>
{
    public async Task Handle(DeleteExternalUserNotificationIdRequest request, CancellationToken cancellationToken)
    {
        var user = await context.Users
            .Where(u => u.Id == bonusUser.UserId)
            .FirstOrDefaultAsync(cancellationToken);

        if (user == null)
        {
            throw new InvalidOperationException($"Authenticated user not found in database");
        }

        var oldValue = user.UserExternalNotificationId;
        user.UserExternalNotificationId = null;

        await context.SaveChangesAsync(cancellationToken);

        logger.LogInformation("Deleted external user notification ID for user {UserId}. Old value: {OldValue}", bonusUser.UserId, oldValue ?? "null");
    }
}
