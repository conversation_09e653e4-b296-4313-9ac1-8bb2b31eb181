using Bonus.Adapters.LSRetail;
using Bonus.Adapters.LSRetail.Enums;
using Bonus.Adapters.LSRetail.Requests;
using Bonus.Resources;
using Bonus.Shared.Helpers;
using Bonus.Shared.Types.Common.Exceptions;
using Bonus.Shared.Types.Interfaces;
using MediatR;

namespace Bonus.Services.Handlers.Home;

public class CreateWalletPassCommand(ILSRetailAdapter lsRetailAdapter, IBonusUser bonusUser)
    : IRequestHandler<CreateWalletPassRequest, CreateWalletPassResponse>
{
    public async Task<CreateWalletPassResponse> Handle(CreateWalletPassRequest request, CancellationToken cancellationToken)
    {
        if (bonusUser.CardId.HasValue() is false)
        {
            throw new BonusException(nameof(Translations.UserNotAuthenticated));
        }
        
        var walletPassRequest = new LSCreateWalletPassRequest
        {
            CardId = bonusUser.CardId,
            PlatformType = Enum.Parse<PlatformType>(request.PlatformType)
        };
            
        var walletPassResponse = await lsRetailAdapter.CreateWalletPass(walletPassRequest, cancellationToken);
            
        return new CreateWalletPassResponse
        {
            Url = walletPassResponse.Response!.Url
        };
    }
}

public class CreateWalletPassRequest : IRequest<CreateWalletPassResponse>
{
    public required string PlatformType { get; set; }
}

public class CreateWalletPassResponse
{
    public required string Url { get; set; }
}