using Bonus.Adapters.LSRetail;
using Bonus.Adapters.LSRetail.Models.Hierarchies;
using Bonus.Adapters.LSRetail.Requests;
using Microsoft.Extensions.Caching.Hybrid;

namespace Bonus.Services.Services;

public interface IHierarchyRawDataService
{
    Task<LSGetHierarchyResponse> GetHierarchyAsync(CancellationToken cancellationToken = default);

    Task<List<HierarchyLeaf>> GetAllProductsAsync(CancellationToken cancellationToken);
}

public class HierarchyRawDataService(ILSRetailAdapter lsRetailAdapter, HybridCache cache) : IHierarchyRawDataService
{
    private const string CacheKey = "hierarchy:raw";
    private readonly TimeSpan CacheExpiration = TimeSpan.FromHours(1);

    public async Task<LSGetHierarchyResponse> GetHierarchyAsync(CancellationToken cancellationToken)
    {
        return await cache.GetOrCreateAsync(
            CacheKey,
            async ct =>
            {
                var lsGetHierarchyRequest = new LSGetHierarchyRequest()
                {
                    StoreId = "01", // TODO: Implement store selection
                };

                var hierarchy = await lsRetailAdapter.GetHierarchy(lsGetHierarchyRequest, ct);

                return hierarchy.Response!;
            },
            new HybridCacheEntryOptions()
            {
                Expiration = CacheExpiration,
                LocalCacheExpiration = CacheExpiration,
            },
            cancellationToken: cancellationToken);
    }

    public async Task<List<HierarchyLeaf>> GetAllProductsAsync(CancellationToken cancellationToken)
    {
        var hierarchy = await GetHierarchyAsync(cancellationToken);
        var rootNode = hierarchy.Hierarchy.FirstOrDefault();

        if (rootNode == null)
        {
            return [];
        }

        return ExtractAllProducts(rootNode.Nodes);
    }

    private List<HierarchyLeaf> ExtractAllProducts(List<HierarchyNode> nodes)
    {
        var products = new List<HierarchyLeaf>();
        var productIds = new HashSet<string>();
        HierarchyNode? vorurNode = null;

        // process all nodes except VÖRUR
        foreach (var node in nodes)
        {
            if (node.Id == "VÖRUR")
            {
                vorurNode = node;
                continue;
            }

            if (node.Leafs != null && node.Leafs.Any())
            {
                foreach (var leaf in node.Leafs)
                {
                    products.Add(leaf);
                    productIds.Add(leaf.Id);
                }
            }

            if (node.Nodes != null && node.Nodes.Any())
            {
                var childProducts = ExtractAllProducts(node.Nodes);
                foreach (var childProduct in childProducts)
                {
                    products.Add(childProduct);
                    productIds.Add(childProduct.Id);
                }
            }
        }

        // Second pass: add products from VÖRUR that don't exist in other categories
        if (vorurNode != null)
        {
            if (vorurNode.Leafs != null && vorurNode.Leafs.Any())
            {
                foreach (var leaf in vorurNode.Leafs)
                {
                    if (!productIds.Contains(leaf.Id)) // Only add if not already present
                    {
                        products.Add(leaf);
                    }
                }
            }

            if (vorurNode.Nodes != null && vorurNode.Nodes.Any())
            {
                var vorurChildProducts = ExtractAllProducts(vorurNode.Nodes);
                foreach (var childProduct in vorurChildProducts)
                {
                    if (!productIds.Contains(childProduct.Id))
                    {
                        products.Add(childProduct);
                    }
                }
            }
        }

        return products;
    }
}

