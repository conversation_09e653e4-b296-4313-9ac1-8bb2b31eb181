using Bonus.Adapters.LSRetail;
using Bonus.Adapters.LSRetail.Models.Hierarchies;
using Bonus.Adapters.LSRetail.Requests;
using Microsoft.Extensions.Caching.Hybrid;

namespace Bonus.Services.Services;

public interface IHierarchyRawDataService
{
    Task<LSGetHierarchyResponse> GetHierarchyAsync(CancellationToken cancellationToken = default);

    Task<List<HierarchyLeaf>> GetAllProductsAsync(CancellationToken cancellationToken);
}

public class HierarchyRawDataService(ILSRetailAdapter lsRetailAdapter, HybridCache cache) : IHierarchyRawDataService
{
    private const string CacheKey = "hierarchy:raw";
    private readonly TimeSpan CacheExpiration = TimeSpan.FromHours(1);

    public async Task<LSGetHierarchyResponse> GetHierarchyAsync(CancellationToken cancellationToken)
    {
        return await cache.GetOrCreateAsync(
            CacheKey,
            async ct =>
            {
                var lsGetHierarchyRequest = new LSGetHierarchyRequest()
                {
                    StoreId = "01", // TODO: Implement store selection
                };

                var hierarchy = await lsRetailAdapter.GetHierarchy(lsGetHierarchyRequest, ct);

                return hierarchy.Response!;
            },
            new HybridCacheEntryOptions()
            {
                Expiration = CacheExpiration,
                LocalCacheExpiration = CacheExpiration,
            },
            cancellationToken: cancellationToken);
    }

    public async Task<List<HierarchyLeaf>> GetAllProductsAsync(CancellationToken cancellationToken)
    {
        var hierarchy = await GetHierarchyAsync(cancellationToken);
        var rootNode = hierarchy.Hierarchy.FirstOrDefault();

        if (rootNode == null)
        {
            return [];
        }

        return ExtractAllProducts(rootNode.Nodes);
    }

    private List<HierarchyLeaf> ExtractAllProducts(List<HierarchyNode> nodes)
    {
        var products = new List<HierarchyLeaf>();

        foreach (var node in nodes)
        {
            if (node.Id == "VÖRUR")
            {
                var allProductsNode = node;
                continue;
            }

            if (node.Leafs != null && node.Leafs.Any())
            {
                products.AddRange(node.Leafs);
            }

            if (node.Nodes != null && node.Nodes.Any())
            {
                products.AddRange(ExtractAllProducts(node.Nodes));
            }
        }

        return products;
    }
}

