using Bonus.Adapters.HagarId;
using Bonus.Adapters.HagarId.Models;
using Bonus.Core.Types.Common.Enums;
using Bonus.Resources;
using Bonus.Shared.Configuration.Settings;
using Microsoft.Extensions.Options;

namespace Bonus.Services.Services;

public interface IHagarIdAuthenticationService
{
    Task<HagarIdStartAuthResponse?> StartAuthentication(string nationalId, CredentialType credentialType, CancellationToken cancellationToken = default);
    
    Task<HagarIdCheckAuthResponse?> CheckAuthenticationStatus(string sessionId, CancellationToken cancellationToken = default);
    
    Task<HagarIdAcceptTermsResponse?> AcceptTerms(string nationalId, string ipAddress, CancellationToken cancellationToken = default);
}

public class HagarIdAuthenticationService(IHagarIdApi hagarIdApi, IOptionsSnapshot<HagarIdApiSettings> settings) : IHagarIdAuthenticationService
{

    public async Task<HagarIdStartAuthResponse?> StartAuthentication(string nationalId, CredentialType credentialType, CancellationToken cancellationToken = default)
    {
        var request = new HagarIdStartAuthRequest
        {
            NationalId = credentialType is CredentialType.Ssn ? nationalId : null,
            PhoneNumber = credentialType is CredentialType.PhoneNumber ? nationalId : null,
            Company = settings.Value.Company,
            Message = Translations.Login_HagarIdStartAuthMessage
        };

        var response = await hagarIdApi.StartAuth(request, settings.Value.StartAuthCode);

        return response;
    }

    public async Task<HagarIdCheckAuthResponse?> CheckAuthenticationStatus(string sessionId, CancellationToken cancellationToken = default)
    {
        var request = new HagarIdCheckAuthRequest
        {
            SessionId = sessionId
        };

        var response = await hagarIdApi.CheckAuthStatus(request, settings.Value.CheckAuthCode);
        
        return response;
    }

    public async Task<HagarIdAcceptTermsResponse?> AcceptTerms(string nationalId, string ipAddress, CancellationToken cancellationToken = default)
    {
        var request = new HagarIdAcceptTermsRequest()
        {
            NationalId = nationalId,
            IpAddress = ipAddress,
            Accepted = true,
            Service = settings.Value.Service,
            Company = settings.Value.Company,
        };

        var response = await hagarIdApi.AcceptTerms(request, settings.Value.AcceptTermsCode);

        return response;
    }
}
