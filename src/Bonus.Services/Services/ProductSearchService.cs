using Bonus.Adapters.LSRetail.Models.Hierarchies;
using Bonus.Core.Data;
using Bonus.Core.Data.Entities.Search;
using Bonus.Shared.Helpers;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using SmartComponents.LocalEmbeddings;

namespace Bonus.Services.Services;

public interface IProductSearchService
{
    Task IndexAllItemsAsync(CancellationToken cancellationToken = default);

    Task<List<SearchResultItem>> SearchItemsAsync(string query, int limit = 10, CancellationToken cancellationToken = default);
}

public record SearchResultItem(string Id, float Similarity); 

public class ProductSearchService(
    IHierarchyRawDataService hierarchyRawDataService,
    BonusContext dbContext,
    LocalEmbedder embedder,
    ILogger<ProductSearchService> logger) : IProductSearchService
{
    
    public async Task IndexAllItemsAsync(CancellationToken cancellationToken = default)
    {
        logger.LogInformation("Starting semantic indexing process");

        var products = await hierarchyRawDataService.GetAllProductsAsync(cancellationToken);
        logger.LogInformation("Found {ProductCount} products to index", products.Count);
        var batches = products.Chunk(500).ToList();

        foreach (var batch in batches)
        {
            await ProcessBatchAsync(batch.ToList(), cancellationToken);
        }

        logger.LogInformation("Completed semantic indexing process");
    }
    
    /// <summary>
    /// Returns closest item (product) ids
    /// </summary>
    /// <param name="query"></param>
    /// <param name="limit"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    public async Task<List<SearchResultItem>> SearchItemsAsync(string query, int limit = 10, CancellationToken cancellationToken = default)
    {
        var embeddings = await dbContext.ProductSearchEmbeddings
            .Select(x => new { x.ItemId, x.EmbeddingSource, x.Embedding })
            .ToListAsync();

        return LocalEmbedder.FindClosestWithScore(
            embedder.Embed(query),
            embeddings.Select(x => (x.ItemId, new EmbeddingF32(x.Embedding))),
            maxResults: limit)
            .Select(x => new SearchResultItem(x.Item, x.Similarity))
            .ToList();
    }
    
    private async Task ProcessBatchAsync(List<HierarchyLeaf> products, CancellationToken cancellationToken)
    {
        var uniqueProducts = products
            .GroupBy(p => p.Id)
            .Select(g => g.First())
            .ToList();
        
        var productIds = uniqueProducts.Select(p => p.Id)
            .ToList();

        var existingItems = await dbContext.ProductSearchEmbeddings
            .Where(e => productIds.Contains(e.ItemId))
            .ToDictionaryAsync(e => e.ItemId, cancellationToken);

        foreach (var product in uniqueProducts)
        {
            if (product.Description.HasValue() is false)
            {
                logger.LogWarning("Product {ProductId} has no description, skipping", product.Id);
                continue;
            }

            var embedding = embedder.Embed(product.Description!).Buffer.ToArray();

            if (existingItems.TryGetValue(product.Id, out var existingItem))
            {
                existingItem.Embedding = embedding;
                existingItem.EmbeddingSource = product.Description!;
            }
            else
            {
                dbContext.ProductSearchEmbeddings.Add(new ProductSearchEmbedding
                {
                    ItemId = product.Id,
                    EmbeddingSource = product.Description!,
                    Embedding = embedding
                });
            }
        }

        await dbContext.SaveChangesAsync(cancellationToken);
    }

}
