using Bonus.Shared.Configuration.Settings;
using Bonus.Shared.Helpers;
using Microsoft.Extensions.Options;

namespace Bonus.Services.Services;

public interface IImageUrlService
{
    string CreateImageUrl(string? imageId);
}

public class ImageUrlService(IOptions<EnvironmentSettings> envSettings) : IImageUrlService 
{
    public string CreateImageUrl(string? imageId)
    {
        if (imageId.HasValue() is false)
        {
            return string.Empty;
        }
        
        return $"{envSettings.Value.BaseUrl.TrimEnd('/')}/api/images/{imageId}";
    }
}