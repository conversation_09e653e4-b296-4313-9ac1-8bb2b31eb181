namespace Bonus.Services.Services;

public interface IAccountService
{
    string CreateTempEmail(string hagarIdentifier);

    bool IsTempEmail(string? email);
}

public class AccountService : IAccountService 
{
    private const string TempEmail = "@temp-bonus.is";

    public string CreateTempEmail(string hagarIdentifier)
    {
        return $"{hagarIdentifier}{TempEmail}";
    }
    
    public bool IsTempEmail(string? email)
    {
        if (string.IsNullOrEmpty(email))
        {
            return false;
        }
        
        return email.EndsWith(TempEmail);
    }
}