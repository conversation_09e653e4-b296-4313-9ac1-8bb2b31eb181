using Bonus.Adapters.LSRetail;
using Bonus.Adapters.LSRetail.Requests;
using Bonus.Core.Data;
using Bonus.Core.Data.Entities;
using Bonus.Core.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Bonus.Services.Services;

public interface IProductSyncService
{
    Task SyncProductsAsync(CancellationToken cancellationToken = default);
    Task SyncProductsBatchAsync(string lastKey = "", int batchSize = 1000, CancellationToken cancellationToken = default);
}

public class ProductSyncService(
    ILSRetailAdapter lsRetailAdapter,
    BonusContext dbContext,
    ISystemTime systemTime,
    ILogger<ProductSyncService> logger) : IProductSyncService
{
    public async Task SyncProductsAsync(CancellationToken cancellationToken = default)
    {
        logger.LogInformation("Starting full product synchronization");
        
        var lastKey = "";
        var totalProcessed = 0;
        
        do
        {
            var batchResult = await SyncProductsBatchInternalAsync(lastKey, 1000, cancellationToken);
            totalProcessed += batchResult.ProcessedCount;
            lastKey = batchResult.LastKey;
            
            logger.LogInformation("Processed batch: {ProcessedCount} items, Total: {TotalProcessed}, LastKey: {LastKey}", 
                batchResult.ProcessedCount, totalProcessed, lastKey);
                
        } while (!string.IsNullOrEmpty(lastKey));
        
        logger.LogInformation("Completed full product synchronization. Total processed: {TotalProcessed}", totalProcessed);
    }

    public async Task SyncProductsBatchAsync(string lastKey = "", int batchSize = 1000, CancellationToken cancellationToken = default)
    {
        var result = await SyncProductsBatchInternalAsync(lastKey, batchSize, cancellationToken);
        logger.LogInformation("Batch sync completed. Processed: {ProcessedCount}, LastKey: {LastKey}", 
            result.ProcessedCount, result.LastKey);
    }

    private async Task<BatchSyncResult> SyncProductsBatchInternalAsync(string lastKey, int batchSize, CancellationToken cancellationToken)
    {
        var request = new LSReplEcommItemsRequest
        {
            ReplRequest = new LSReplRequest
            {
                AppId = "BonusApp",
                StoreId = "01",
                TerminalId = "MOBILE",
                BatchSize = batchSize,
                FullReplication = true,
                LastKey = lastKey,
                MaxKey = ""
            }
        };

        var response = await lsRetailAdapter.ReplEcommItems(request, cancellationToken);
        
        if (response.Response?.Result?.Items == null)
        {
            logger.LogWarning("No items received from LSRetail API");
            return new BatchSyncResult { ProcessedCount = 0, LastKey = "" };
        }

        var items = response.Response.Result.Items;
        var processedCount = 0;
        var syncTime = systemTime.UtcNow;

        foreach (var item in items)
        {
            try
            {
                await UpsertProductAsync(item, syncTime, cancellationToken);
                processedCount++;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to sync product {ItemId}: {Error}", item.Id, ex.Message);
            }
        }

        await dbContext.SaveChangesAsync(cancellationToken);
        
        return new BatchSyncResult 
        { 
            ProcessedCount = processedCount, 
            LastKey = response.Response.Result.LastKey 
        };
    }

    private async Task UpsertProductAsync(LSReplEcommItem item, DateTime syncTime, CancellationToken cancellationToken)
    {
        var existingProduct = await dbContext.Products
            .FirstOrDefaultAsync(p => p.ItemId == item.Id, cancellationToken);

        if (existingProduct != null)
        {
            UpdateProduct(existingProduct, item, syncTime);
        }
        else
        {
            // Create new product
            var newProduct = CreateProduct(item, syncTime);
            dbContext.Products.Add(newProduct);
        }
    }

    private static void UpdateProduct(Product product, LSReplEcommItem item, DateTime syncTime)
    {
        product.Description = item.Description;
        product.Details = item.Details;
        product.BaseUnitOfMeasure = item.BaseUnitOfMeasure;
        product.SalesUnitOfMeasure = item.SalesUnitOfMeasure;
        product.PurchaseUnitOfMeasure = item.PurchaseUnitOfMeasure;
        product.UnitPrice = item.UnitPrice;
        product.GrossWeight = item.GrossWeight;
        product.UnitVolume = item.UnitVolume;
        product.UnitsPerParcel = item.UnitsPerParcel;
        product.ItemCategoryCode = item.ItemCategoryCode;
        product.ItemFamilyCode = item.ItemFamilyCode;
        product.ItemTrackingCode = item.ItemTrackingCode;
        product.ProductGroupId = item.ProductGroupId;
        product.SeasonCode = item.SeasonCode;
        product.TariffNo = item.TariffNo;
        product.TaxItemGroupId = item.TaxItemGroupId;
        product.VendorId = item.VendorId;
        product.VendorItemId = item.VendorItemId;
        product.CountryOfOrigin = item.CountryOfOrigin;
        product.Type = item.Type;
        product.IsBlocked = item.Blocked == 1;
        product.IsBlockedOnPos = item.BlockedOnPos == 1;
        product.IsBlockedOnECom = item.BlockedOnECom == 1;
        product.BlockDiscount = item.BlockDiscount == 1;
        product.BlockDistribution = item.BlockDistribution == 1;
        product.BlockManualPriceChange = item.BlockManualPriceChange == 1;
        product.BlockNegativeAdjustment = item.BlockNegativeAdjustment == 1;
        product.BlockPositiveAdjustment = item.BlockPositiveAdjustment == 1;
        product.BlockPurchaseReturn = item.BlockPurchaseReturn == 1;
        product.KeyingInPrice = item.KeyingInPrice == 1;
        product.KeyingInQty = item.KeyingInQty == 1;
        product.MustKeyInComment = item.MustKeyInComment == 1;
        product.NoDiscountAllowed = item.NoDiscountAllowed == 1;
        product.IsScaleItem = item.ScaleItem == 1;
        product.ZeroPriceValidation = item.ZeroPriceValidation == 1;
        product.CrossSellingExists = item.CrossSellingExists == 1;
        product.IsDeleted = item.IsDeleted;
        product.LastSyncTime = syncTime;
    }

    private static Product CreateProduct(LSReplEcommItem item, DateTime syncTime)
    {
        return new Product
        {
            ItemId = item.Id,
            Description = item.Description,
            Details = item.Details,
            BaseUnitOfMeasure = item.BaseUnitOfMeasure,
            SalesUnitOfMeasure = item.SalesUnitOfMeasure,
            PurchaseUnitOfMeasure = item.PurchaseUnitOfMeasure,
            UnitPrice = item.UnitPrice,
            GrossWeight = item.GrossWeight,
            UnitVolume = item.UnitVolume,
            UnitsPerParcel = item.UnitsPerParcel,
            ItemCategoryCode = item.ItemCategoryCode,
            ItemFamilyCode = item.ItemFamilyCode,
            ItemTrackingCode = item.ItemTrackingCode,
            ProductGroupId = item.ProductGroupId,
            SeasonCode = item.SeasonCode,
            TariffNo = item.TariffNo,
            TaxItemGroupId = item.TaxItemGroupId,
            VendorId = item.VendorId,
            VendorItemId = item.VendorItemId,
            CountryOfOrigin = item.CountryOfOrigin,
            Type = item.Type,
            IsBlocked = item.Blocked == 1,
            IsBlockedOnPos = item.BlockedOnPos == 1,
            IsBlockedOnECom = item.BlockedOnECom == 1,
            BlockDiscount = item.BlockDiscount == 1,
            BlockDistribution = item.BlockDistribution == 1,
            BlockManualPriceChange = item.BlockManualPriceChange == 1,
            BlockNegativeAdjustment = item.BlockNegativeAdjustment == 1,
            BlockPositiveAdjustment = item.BlockPositiveAdjustment == 1,
            BlockPurchaseReturn = item.BlockPurchaseReturn == 1,
            KeyingInPrice = item.KeyingInPrice == 1,
            KeyingInQty = item.KeyingInQty == 1,
            MustKeyInComment = item.MustKeyInComment == 1,
            NoDiscountAllowed = item.NoDiscountAllowed == 1,
            IsScaleItem = item.ScaleItem == 1,
            ZeroPriceValidation = item.ZeroPriceValidation == 1,
            CrossSellingExists = item.CrossSellingExists == 1,
            IsDeleted = item.IsDeleted,
            LastSyncTime = syncTime,
            CreatedTime = syncTime
        };
    }

    private class BatchSyncResult
    {
        public int ProcessedCount { get; set; }
        public string LastKey { get; set; } = string.Empty;
    }
}
