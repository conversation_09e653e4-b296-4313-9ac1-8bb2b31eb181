using Bonus.Adapters.Configuration;
using Bonus.Core.Configuration.Behaviours;
using Bonus.Core.Data;
using Bonus.Core.Services;
using Bonus.Shared.Helpers;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Serilog;
using Serilog.Enrichers.Span;
using Serilog.Events;
using Serilog.Exceptions;
using Serilog.Exceptions.Core;
using Serilog.Exceptions.Refit.Destructurers;
using Serilog.Sinks.SystemConsole.Themes;
using Serilog.Sinks.MSSqlServer;

namespace Bonus.Core.Configuration;

public static class ServiceConfiguration
{
    public static void AddCoreServices(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddMediatR(config =>
        {
            config.RegisterServicesFromAssemblyContaining(typeof(ServiceConfiguration));
            config.AddOpenBehavior(typeof(LoggingBehaviour<,>));
        });
        
        services.AddDbContext<BonusContext>((serviceProvider, options) =>
        {
            options.UseSqlServer(configuration.GetConnectionString(nameof(BonusContext)));
            options.UseSnakeCaseNamingConvention();
        });
        
        services.AddHybridCache(options =>
        {
            options.MaximumPayloadBytes = 1024 * 1024 * 100; // 100 MB
        });

        services.AddSingleton<ISystemTime, SystemTime>();

        services.AddTransient<IAuthenticationService, AuthenticationService>();
        
        services.AddBonusAdapters(configuration);
    }
    
    public static IHostBuilder AddSerilog(this IHostBuilder host, string appName)
    {
        Serilog.Debugging.SelfLog.Enable(Console.Error);

        host.UseSerilog((hostBuilderContext, loggerConfiguration) =>
        {
            var isDevelopment = hostBuilderContext.HostingEnvironment.IsDevelopment();
            var configuration = hostBuilderContext.Configuration;

            loggerConfiguration
                .MinimumLevel.Override("Microsoft", LogEventLevel.Information)
                .MinimumLevel.Override("Microsoft.AspNetCore", LogEventLevel.Warning)
                .MinimumLevel.Override("Microsoft.AspNetCore.Authentication", LogEventLevel.Information)
                .MinimumLevel.Override("Microsoft.EntityFrameworkCore", LogEventLevel.Warning)
                .MinimumLevel.Override("Microsoft.EntityFrameworkCore.Database.Command", LogEventLevel.Warning)
                .Enrich.WithSpan()
                .Enrich.FromLogContext()
                .Enrich.WithProperty("ApplicationName", appName)
                .Enrich.WithExceptionDetails(new DestructuringOptionsBuilder()
                    .WithDefaultDestructurers()
                    .WithDestructurers(
                        [
                            new ApiExceptionDestructurer(destructureHttpContent: true)
                        ]
                    )
                );

            // Add database logging
            var connectionString = configuration.GetConnectionString(nameof(BonusContext));
            if (connectionString.HasValue())
            {
                var sinkOptions = new MSSqlServerSinkOptions
                {
                    TableName = "application_logs",
                    AutoCreateSqlTable = true
                };

                var columnOptions = new ColumnOptions();
                columnOptions.Store.Remove(StandardColumn.MessageTemplate);
                columnOptions.Store.Remove(StandardColumn.Properties);
                columnOptions.Store.Add(StandardColumn.LogEvent);
                columnOptions.Store.Add(StandardColumn.TraceId);
                columnOptions.Store.Add(StandardColumn.SpanId);

                loggerConfiguration.WriteTo.Async(x =>
                    x.MSSqlServer(connectionString, sinkOptions, columnOptions: columnOptions));
            }
            
            var outputTemplate = "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}";
            loggerConfiguration.WriteTo.Async(x =>
                x.Console(theme: AnsiConsoleTheme.Code, outputTemplate: outputTemplate));
        });

        return host;
    }
}