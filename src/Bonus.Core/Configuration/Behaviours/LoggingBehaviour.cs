using System.Diagnostics;
using Bonus.Shared.Types.Common.Exceptions;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Bonus.Core.Configuration.Behaviours;

public class LoggingBehaviour<TRequest, TResponse> : IPipelineBehavior<TRequest, TResponse> where TRequest : IRequest<TResponse>
{
    private readonly ILogger<LoggingBehaviour<TRequest, TResponse>> _logger;

    public LoggingBehaviour(ILogger<LoggingBehaviour<TRequest, TResponse>> logger)
    {
        _logger = logger;
    }

    public async Task<TResponse> Handle(TRequest request, RequestHandlerDelegate<TResponse> next, CancellationToken cancellationToken)
    {
        var sw = Stopwatch.StartNew();

        try
        {
            var response = await next();

            LogDuration(true);

            return response;
        }
        catch (BonusException bonusException)
        {
            _logger.LogWarning(bonusException, "Bonus exception occured: {exMessage}", bonusException.Message);

            LogDuration(false);

            throw;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unhandled exception occured: {exMessage}", ex.Message);

            LogDuration(false);

            throw;
        }

        void LogDuration(bool success)
        {
            _logger.LogInformation("Handler {handler} executed in {duration}ms with result {success}", request.GetType().Name, sw!.ElapsedMilliseconds, success);
        }
    }
}