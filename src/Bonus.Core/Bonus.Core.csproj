<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\Bonus.Adapters\Bonus.Adapters.csproj" />
        <ProjectReference Include="..\Bonus.Shared\Bonus.Shared.csproj" />
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="EFCore.NamingConventions" Version="9.0.0" />
        <PackageReference Include="MediatR" Version="12.4.1" />
        <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.5" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.5">
          <PrivateAssets>all</PrivateAssets>
          <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.5" />
        <PackageReference Include="Microsoft.ML.OnnxRuntime" Version="1.22.0" />
        <PackageReference Include="Serilog" Version="4.2.0" />
        <PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
        <PackageReference Include="Serilog.Enrichers.Span" Version="3.1.0" />
        <PackageReference Include="Serilog.Exceptions" Version="8.4.0" />
        <PackageReference Include="Serilog.Exceptions.Refit" Version="8.4.0" />
        <PackageReference Include="Serilog.Sinks.Async" Version="2.1.0" />
        <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
        <PackageReference Include="Serilog.Sinks.MSSqlServer" Version="8.1.0" />
    </ItemGroup>

</Project>
