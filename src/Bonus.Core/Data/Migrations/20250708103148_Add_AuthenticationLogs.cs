using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Bonus.Core.Migrations
{
    /// <inheritdoc />
    public partial class Add_AuthenticationLogs : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql("DELETE FROM refresh_tokens WHERE 1 = 1");
            
            migrationBuilder.AddColumn<int>(
                name: "authentication_log_id",
                table: "refresh_tokens",
                type: "int",
                nullable: false);

            migrationBuilder.CreateTable(
                name: "authentication_logs",
                columns: table => new
                {
                    id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    credential_value = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    credential_type = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    user_id = table.Column<int>(type: "int", nullable: true),
                    error_message = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    is_success = table.Column<bool>(type: "bit", nullable: false),
                    ip_address = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    device_manufacturer = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    device_model_name = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    device_id = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    app_version = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    operating_system = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    operating_system_version = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    created_time = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_authentication_logs", x => x.id);
                    table.ForeignKey(
                        name: "fk_authentication_logs_users_user_id",
                        column: x => x.user_id,
                        principalTable: "users",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "ix_refresh_tokens_authentication_log_id",
                table: "refresh_tokens",
                column: "authentication_log_id");

            migrationBuilder.CreateIndex(
                name: "ix_authentication_logs_user_id",
                table: "authentication_logs",
                column: "user_id");

            migrationBuilder.AddForeignKey(
                name: "fk_refresh_tokens_authentication_logs_authentication_log_id",
                table: "refresh_tokens",
                column: "authentication_log_id",
                principalTable: "authentication_logs",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "fk_refresh_tokens_authentication_logs_authentication_log_id",
                table: "refresh_tokens");

            migrationBuilder.DropTable(
                name: "authentication_logs");

            migrationBuilder.DropIndex(
                name: "ix_refresh_tokens_authentication_log_id",
                table: "refresh_tokens");

            migrationBuilder.DropColumn(
                name: "authentication_log_id",
                table: "refresh_tokens");
        }
    }
}
