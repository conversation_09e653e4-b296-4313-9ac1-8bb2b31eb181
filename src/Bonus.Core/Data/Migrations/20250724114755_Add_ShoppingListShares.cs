using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Bonus.Core.Data.Migrations
{
    /// <inheritdoc />
    public partial class Add_ShoppingListShares : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "shopping_list_shares",
                columns: table => new
                {
                    id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    share_id = table.Column<string>(type: "nvarchar(450)", nullable: false),
                    shopping_list_id = table.Column<string>(type: "nvarchar(450)", nullable: false),
                    linked_card_id = table.Column<string>(type: "nvarchar(450)", nullable: false),
                    created_time = table.Column<DateTime>(type: "datetime2", nullable: false),
                    archived_time = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_shopping_list_shares", x => x.id);
                });

            migrationBuilder.CreateIndex(
                name: "perf_shopping_list_shares",
                table: "shopping_list_shares",
                columns: new[] { "share_id", "shopping_list_id", "linked_card_id" });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "shopping_list_shares");
        }
    }
}
