using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Bonus.Core.Migrations
{
    /// <inheritdoc />
    public partial class ProductSearchEmbeddings : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "product_search_embeddings",
                columns: table => new
                {
                    id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    item_id = table.Column<string>(type: "nvarchar(450)", nullable: false),
                    embedding_source = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    embedding = table.Column<byte[]>(type: "varbinary(max)", nullable: false),
                    created_time = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_product_search_embeddings", x => x.id);
                });

            migrationBuilder.CreateIndex(
                name: "ix_product_search_embeddings_item_id",
                table: "product_search_embeddings",
                column: "item_id",
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "product_search_embeddings");
        }
    }
}
