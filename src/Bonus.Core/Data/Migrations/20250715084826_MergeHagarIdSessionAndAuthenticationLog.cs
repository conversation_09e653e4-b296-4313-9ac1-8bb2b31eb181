using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Bonus.Core.Data.Migrations
{
    /// <inheritdoc />
    public partial class MergeHagarIdSessionAndAuthenticationLog : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "hagar_id_sessions");

            migrationBuilder.AddColumn<DateTime>(
                name: "session_expires_at",
                table: "authentication_logs",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "session_id",
                table: "authentication_logs",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "session_is_completed",
                table: "authentication_logs",
                type: "bit",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "session_expires_at",
                table: "authentication_logs");

            migrationBuilder.DropColumn(
                name: "session_id",
                table: "authentication_logs");

            migrationBuilder.DropColumn(
                name: "session_is_completed",
                table: "authentication_logs");

            migrationBuilder.CreateTable(
                name: "hagar_id_sessions",
                columns: table => new
                {
                    id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    app_version = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    created_time = table.Column<DateTime>(type: "datetime2", nullable: false),
                    credential_type = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    device_id = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    device_manufacturer = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    device_model_name = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    expires_at = table.Column<DateTime>(type: "datetime2", nullable: false),
                    identifier = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ip_address = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    is_completed = table.Column<bool>(type: "bit", nullable: false),
                    operating_system = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    operating_system_version = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    session_id = table.Column<string>(type: "nvarchar(max)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_hagar_id_sessions", x => x.id);
                });
        }
    }
}
