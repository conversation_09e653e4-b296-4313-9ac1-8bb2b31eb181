using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Bonus.Core.Data.Migrations
{
    /// <inheritdoc />
    public partial class Add_ShoppingListItems : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "shopping_list_items",
                columns: table => new
                {
                    id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    shopping_list_id = table.Column<string>(type: "nvarchar(450)", nullable: false),
                    shopping_list_item_id = table.Column<string>(type: "nvarchar(450)", nullable: false),
                    completed = table.Column<bool>(type: "bit", nullable: false),
                    created_time = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_shopping_list_items", x => x.id);
                });

            migrationBuilder.CreateIndex(
                name: "perf_shopping_list_items",
                table: "shopping_list_items",
                columns: new[] { "shopping_list_id", "shopping_list_item_id" });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "shopping_list_items");
        }
    }
}
