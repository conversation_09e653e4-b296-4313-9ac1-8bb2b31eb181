// <auto-generated />
using System;
using Bonus.Core.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace Bonus.Core.Data.Migrations
{
    [DbContext(typeof(BonusContext))]
    [Migration("20250717081358_Rename_ShoppingListItems")]
    partial class Rename_ShoppingListItems
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.5")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("Bonus.Core.Data.Entities.AuthenticationLog", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("AppVersion")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("app_version");

                    b.Property<DateTime>("CreatedTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("created_time");

                    b.Property<string>("CredentialType")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("credential_type");

                    b.Property<string>("CredentialValue")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("credential_value");

                    b.Property<string>("DeviceId")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("device_id");

                    b.Property<string>("DeviceManufacturer")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("device_manufacturer");

                    b.Property<string>("DeviceModelName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("device_model_name");

                    b.Property<string>("ErrorMessage")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("error_message");

                    b.Property<string>("IpAddress")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ip_address");

                    b.Property<bool>("IsSuccess")
                        .HasColumnType("bit")
                        .HasColumnName("is_success");

                    b.Property<string>("OperatingSystem")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("operating_system");

                    b.Property<string>("OperatingSystemVersion")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("operating_system_version");

                    b.Property<int?>("UserId")
                        .HasColumnType("int")
                        .HasColumnName("user_id");

                    b.HasKey("Id")
                        .HasName("pk_authentication_logs");

                    b.HasIndex("UserId")
                        .HasDatabaseName("ix_authentication_logs_user_id");

                    b.ToTable("authentication_logs", (string)null);
                });

            modelBuilder.Entity("Bonus.Core.Data.Entities.Notification", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime?>("ArchivedTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("archived_time");

                    b.Property<DateTime>("CreatedTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("created_time");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("description");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("title");

                    b.HasKey("Id")
                        .HasName("pk_notifications");

                    b.ToTable("notifications", (string)null);
                });

            modelBuilder.Entity("Bonus.Core.Data.Entities.NotificationRecipient", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("created_time");

                    b.Property<int>("NotificationId")
                        .HasColumnType("int")
                        .HasColumnName("notification_id");

                    b.Property<DateTime?>("ReadTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("read_time");

                    b.Property<int>("UserId")
                        .HasColumnType("int")
                        .HasColumnName("user_id");

                    b.HasKey("Id")
                        .HasName("pk_notification_recipients");

                    b.HasIndex("NotificationId")
                        .HasDatabaseName("ix_notification_recipients_notification_id");

                    b.HasIndex("UserId")
                        .HasDatabaseName("ix_notification_recipients_user_id");

                    b.ToTable("notification_recipients", (string)null);
                });

            modelBuilder.Entity("Bonus.Core.Data.Entities.RefreshToken", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime?>("ArchivedTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("archived_time");

                    b.Property<int>("AuthenticationLogId")
                        .HasColumnType("int")
                        .HasColumnName("authentication_log_id");

                    b.Property<DateTime>("CreatedTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("created_time");

                    b.Property<string>("Token")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("token");

                    b.Property<DateTime?>("UsedOn")
                        .HasColumnType("datetime2")
                        .HasColumnName("used_on");

                    b.Property<int>("UserId")
                        .HasColumnType("int")
                        .HasColumnName("user_id");

                    b.Property<DateTime>("ValidUntil")
                        .HasColumnType("datetime2")
                        .HasColumnName("valid_until");

                    b.HasKey("Id")
                        .HasName("pk_refresh_tokens");

                    b.HasIndex("AuthenticationLogId")
                        .HasDatabaseName("ix_refresh_tokens_authentication_log_id");

                    b.HasIndex("UserId")
                        .HasDatabaseName("ix_refresh_tokens_user_id");

                    b.ToTable("refresh_tokens", (string)null);
                });

            modelBuilder.Entity("Bonus.Core.Data.Entities.Search.ProductSearchEmbedding", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("created_time");

                    b.Property<byte[]>("Embedding")
                        .IsRequired()
                        .HasColumnType("varbinary(max)")
                        .HasColumnName("embedding");

                    b.Property<string>("EmbeddingSource")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("embedding_source");

                    b.Property<string>("ItemId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)")
                        .HasColumnName("item_id");

                    b.HasKey("Id")
                        .HasName("pk_product_search_embeddings");

                    b.HasIndex("ItemId")
                        .IsUnique()
                        .HasDatabaseName("ix_product_search_embeddings_item_id");

                    b.ToTable("product_search_embeddings", (string)null);
                });

            modelBuilder.Entity("Bonus.Core.Data.Entities.ShoppingListItem", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool>("Completed")
                        .HasColumnType("bit")
                        .HasColumnName("completed");

                    b.Property<DateTime>("CreatedTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("created_time");

                    b.Property<string>("ItemDescription")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)")
                        .HasColumnName("item_description");

                    b.Property<string>("ShoppingListId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)")
                        .HasColumnName("shopping_list_id");

                    b.HasKey("Id")
                        .HasName("pk_shopping_list_items");

                    b.HasIndex("ShoppingListId", "ItemDescription")
                        .HasDatabaseName("perf_shopping_list_items");

                    b.ToTable("shopping_list_items", (string)null);
                });

            modelBuilder.Entity("Bonus.Core.Data.Entities.User", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("AccountId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("account_id");

                    b.Property<DateTime?>("ArchivedTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("archived_time");

                    b.Property<string>("AuthenticationId")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("authentication_id");

                    b.Property<string>("CardId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("card_id");

                    b.Property<DateTime>("CreatedTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("created_time");

                    b.Property<string>("MemberContactId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("member_contact_id");

                    b.HasKey("Id")
                        .HasName("pk_users");

                    b.ToTable("users", (string)null);
                });

            modelBuilder.Entity("Bonus.Core.Data.Entities.AuthenticationLog", b =>
                {
                    b.HasOne("Bonus.Core.Data.Entities.User", "User")
                        .WithMany("AuthenticationLogs")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_authentication_logs_users_user_id");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Bonus.Core.Data.Entities.NotificationRecipient", b =>
                {
                    b.HasOne("Bonus.Core.Data.Entities.Notification", "Notification")
                        .WithMany("Recipients")
                        .HasForeignKey("NotificationId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_notification_recipients_notifications_notification_id");

                    b.HasOne("Bonus.Core.Data.Entities.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_notification_recipients_users_user_id");

                    b.Navigation("Notification");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Bonus.Core.Data.Entities.RefreshToken", b =>
                {
                    b.HasOne("Bonus.Core.Data.Entities.AuthenticationLog", "AuthenticationLog")
                        .WithMany()
                        .HasForeignKey("AuthenticationLogId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_refresh_tokens_authentication_logs_authentication_log_id");

                    b.HasOne("Bonus.Core.Data.Entities.User", "User")
                        .WithMany("RefreshTokens")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_refresh_tokens_users_user_id");

                    b.Navigation("AuthenticationLog");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Bonus.Core.Data.Entities.Notification", b =>
                {
                    b.Navigation("Recipients");
                });

            modelBuilder.Entity("Bonus.Core.Data.Entities.User", b =>
                {
                    b.Navigation("AuthenticationLogs");

                    b.Navigation("RefreshTokens");
                });
#pragma warning restore 612, 618
        }
    }
}
