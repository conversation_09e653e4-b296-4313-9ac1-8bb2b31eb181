using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Bonus.Core.Migrations
{
    /// <inheritdoc />
    public partial class HagarIdSession : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "hagar_id_sessions",
                columns: table => new
                {
                    id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    session_id = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    identifier = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    credential_type = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    device_id = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    device_manufacturer = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    device_model_name = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    operating_system = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    operating_system_version = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    app_version = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ip_address = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    expires_at = table.Column<DateTime>(type: "datetime2", nullable: false),
                    is_completed = table.Column<bool>(type: "bit", nullable: false),
                    created_time = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_hagar_id_sessions", x => x.id);
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "hagar_id_sessions");
        }
    }
}
