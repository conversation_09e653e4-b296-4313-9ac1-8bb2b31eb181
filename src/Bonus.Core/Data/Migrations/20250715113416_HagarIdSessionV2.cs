using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Bonus.Core.Data.Migrations
{
    /// <inheritdoc />
    public partial class HagarIdSessionV2 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "session_expires_at",
                table: "authentication_logs");

            migrationBuilder.DropColumn(
                name: "session_id",
                table: "authentication_logs");

            migrationBuilder.DropColumn(
                name: "session_is_completed",
                table: "authentication_logs");

            migrationBuilder.AddColumn<int>(
                name: "hagar_id_session_id",
                table: "authentication_logs",
                type: "int",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "hagar_id_sessions",
                columns: table => new
                {
                    id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    session_id = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    expires_at = table.Column<DateTime>(type: "datetime2", nullable: false),
                    is_completed = table.Column<bool>(type: "bit", nullable: false),
                    created_time = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_hagar_id_sessions", x => x.id);
                });

            migrationBuilder.CreateIndex(
                name: "ix_authentication_logs_hagar_id_session_id",
                table: "authentication_logs",
                column: "hagar_id_session_id");

            migrationBuilder.AddForeignKey(
                name: "fk_authentication_logs_hagar_id_sessions_hagar_id_session_id",
                table: "authentication_logs",
                column: "hagar_id_session_id",
                principalTable: "hagar_id_sessions",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "fk_authentication_logs_hagar_id_sessions_hagar_id_session_id",
                table: "authentication_logs");

            migrationBuilder.DropTable(
                name: "hagar_id_sessions");

            migrationBuilder.DropIndex(
                name: "ix_authentication_logs_hagar_id_session_id",
                table: "authentication_logs");

            migrationBuilder.DropColumn(
                name: "hagar_id_session_id",
                table: "authentication_logs");

            migrationBuilder.AddColumn<DateTime>(
                name: "session_expires_at",
                table: "authentication_logs",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "session_id",
                table: "authentication_logs",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "session_is_completed",
                table: "authentication_logs",
                type: "bit",
                nullable: true);
        }
    }
}
