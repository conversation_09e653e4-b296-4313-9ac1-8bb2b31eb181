using Bonus.Shared.Data.Entities.Base;

namespace Bonus.Core.Data.Entities;

public class Promotion : BaseEntity, IVersionEntity
{
    public required string Title { get; set; }
    
    public required string? Description { get; set; }
    
    public required string Base64Image { get; set; }
    
    public required string? LSRetailStoreId { get; set; }
    
    public required DateTime ValidFrom { get; set; }
    
    public required DateTime? ValidTo { get; set; }
}