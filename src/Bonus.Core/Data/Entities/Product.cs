using Bonus.Shared.Data.Entities.Base;

namespace Bonus.Core.Data.Entities;

public class Product : BaseEntity
{
    public required string ItemId { get; set; }
    
    public string? Description { get; set; }
    
    public string? Details { get; set; }
    
    public string? BaseUnitOfMeasure { get; set; }
    
    public string? SalesUnitOfMeasure { get; set; }
    
    public string? PurchaseUnitOfMeasure { get; set; }
    
    public decimal UnitPrice { get; set; }
    
    public decimal GrossWeight { get; set; }
    
    public decimal UnitVolume { get; set; }
    
    public decimal UnitsPerParcel { get; set; }
    
    public string? ItemCategoryCode { get; set; }
    
    public string? ItemFamilyCode { get; set; }
    
    public string? ItemTrackingCode { get; set; }
    
    public string? ProductGroupId { get; set; }
    
    public string? SeasonCode { get; set; }
    
    public string? TariffNo { get; set; }
    
    public string? TaxItemGroupId { get; set; }
    
    public string? VendorId { get; set; }
    
    public string? VendorItemId { get; set; }
    
    public string? CountryOfOrigin { get; set; }
    
    public int Type { get; set; }
    
    public bool IsBlocked { get; set; }
    
    public bool IsBlockedOnPos { get; set; }
    
    public bool IsBlockedOnECom { get; set; }
    
    public bool BlockDiscount { get; set; }
    
    public bool BlockDistribution { get; set; }
    
    public bool BlockManualPriceChange { get; set; }
    
    public bool BlockNegativeAdjustment { get; set; }
    
    public bool BlockPositiveAdjustment { get; set; }
    
    public bool BlockPurchaseReturn { get; set; }
    
    public bool KeyingInPrice { get; set; }
    
    public bool KeyingInQty { get; set; }
    
    public bool MustKeyInComment { get; set; }
    
    public bool NoDiscountAllowed { get; set; }
    
    public bool IsScaleItem { get; set; }
    
    public bool ZeroPriceValidation { get; set; }
    
    public bool CrossSellingExists { get; set; }
    
    public bool IsDeleted { get; set; }
    
    public DateTime LastSyncTime { get; set; }
}
