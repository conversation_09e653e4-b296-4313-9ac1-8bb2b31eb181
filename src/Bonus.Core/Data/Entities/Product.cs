using Bonus.Shared.Data.Entities.Base;

namespace Bonus.Core.Data.Entities;

public class Product : BaseEntity
{
    public required string ItemId { get; set; }

    public string? Description { get; set; }

    public string? Details { get; set; }

    public decimal UnitPrice { get; set; }

    public string? ItemCategoryCode { get; set; }

    public string? SalesUnitOfMeasure { get; set; }

    public bool IsDeleted { get; set; }

    public DateTime LastSyncTime { get; set; }

}
