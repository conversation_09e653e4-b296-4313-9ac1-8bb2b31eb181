using System.ComponentModel.DataAnnotations;
using Bonus.Shared.Data.Entities.Base;

namespace Bonus.Core.Data.Entities;

public class Notification : BaseArchivableEntity
{
    [MaxLength(255)]
    public required string Title { get; set; }
    
    public required string Description { get; set; }

    public DateTime? SentAt { get; set; }

    public ICollection<NotificationRecipient> Recipients { get; set; } = new List<NotificationRecipient>();
}
