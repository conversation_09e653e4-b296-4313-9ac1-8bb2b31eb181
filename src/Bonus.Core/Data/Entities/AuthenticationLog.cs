using Bonus.Core.Types.Common.Enums;
using Bonus.Shared.Data.Entities.Base;

namespace Bonus.Core.Data.Entities;

public class AuthenticationLog : BaseEntity
{
    public required string CredentialValue { get; set; }
    
    public required CredentialType CredentialType { get; set; }

    public required int? UserId { get; set; }
    
    public required string? ErrorMessage { get; set; }

    public required bool IsSuccess { get; set; }

    public required string IpAddress { get; set; }
    
    public required string? DeviceManufacturer { get; set; }
    
    public required string? DeviceModelName { get; set; }
    
    public required string? DeviceId { get; set; }
    
    public required string? AppVersion { get; set; }
    
    public required string? OperatingSystem { get; set; }
    
    public required string? OperatingSystemVersion { get; set; }

    public int? HagarIdSessionId { get; set; }

    public HagarIdSession? HagarIdSession { get; set; }

    public User? User { get; set; }
}

