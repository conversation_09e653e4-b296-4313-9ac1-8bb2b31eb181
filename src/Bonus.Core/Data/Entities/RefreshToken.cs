using Bonus.Shared.Data.Entities.Base;

namespace Bonus.Core.Data.Entities;

public class RefreshToken : BaseArchivableEntity
{
    public int UserId { get; set; }
    
    public int AuthenticationLogId { get; set; }

    public required string Token { get; set; }

    public DateTime ValidUntil { get; set; }

    public DateTime? UsedOn { get; set; }
    
    public User User { get; set; } = null!;
    
    public AuthenticationLog AuthenticationLog { get; set; } = null!;
}