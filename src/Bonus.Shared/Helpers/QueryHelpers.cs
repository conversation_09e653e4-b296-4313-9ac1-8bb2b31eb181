using System.Linq.Expressions;
using Bonus.Shared.Types.Common.Exceptions;

namespace Bonus.Shared.Helpers;

public static class QueryHelpers
{
    /// <summary>
    /// Applies where selector if condition is true
    /// </summary>
    public static IQueryable<T> WhereIf<T>(this IQueryable<T> source, bool condition, Expression<Func<T, bool>> selector)
    {
        if (!condition)
        {
            return source;
        }

        return source.Where(selector);
    }
    
    /// <summary>
    /// Applies where selector if condition is true
    /// </summary>
    public static IEnumerable<T> WhereIf<T>(this IEnumerable<T> source, bool condition, Func<T, bool> selector)
    {
        if (!condition)
        {
            return source;
        }

        return source.Where(selector);
    }

    /// <summary>
    /// Where condition that checks if selected datetime is in range of selected dates (ignoring time, and date to is set to next day at 00:00 exclusive)
    /// </summary>
    /// <typeparam name="T">DbSet</typeparam>
    /// <param name="source">Query</param>
    /// <param name="selector">Parameter that need to be filtered.</param>
    /// <param name="dateFrom">Inclusive datetime.</param>
    /// <param name="dateTo">Exclusive datetime.</param>
    public static IQueryable<T> WhereDateInRange<T>(this IQueryable<T> source, Expression<Func<T, DateTime?>> selector, DateTime? dateFrom, DateTime? dateTo)
    {
        if (dateFrom.HasValue)
        {
            dateFrom = dateFrom.Value.Date;
        }

        if (dateTo.HasValue)
        {
            dateTo = dateTo.Value.Date.AddDays(1);
        }

        return WhereDateTimeInRange(source, selector, dateFrom, dateTo);
    }

    /// <summary>
    /// Where condition that checks if selected non UTC datetime is in range of selected dates (ignoring time, and date to is set to next day at 00:00 exclusive)
    /// </summary>
    /// <typeparam name="T">DbSet</typeparam>
    /// <param name="source">Query</param>
    /// <param name="selector">Parameter that need to be filtered.</param>
    /// <param name="dateFrom">Inclusive datetime.</param>
    /// <param name="dateTo">Exclusive datetime.</param>
    public static IQueryable<T> WhereUnspecifiedDateInRange<T>(this IQueryable<T> source, Expression<Func<T, DateTime?>> selector, DateTime? dateFrom, DateTime? dateTo)
    {
        if (dateFrom.HasValue)
        {
            dateFrom = dateFrom.Value.Date.ToUnspecifiedTime();
        }

        if (dateTo.HasValue)
        {
            dateTo = dateTo.Value.Date.AddDays(1).ToUnspecifiedTime();
        }

        return WhereDateTimeInRange(source, selector, dateFrom, dateTo);
    }

    /// <summary>
    /// Where condition that checks if selected datetime is in range of selected datetimes.
    /// </summary>
    /// <typeparam name="T">DbSet</typeparam>
    /// <param name="source">Query</param>
    /// <param name="selector">Parameter that need to be filtered.</param>
    /// <param name="dateFrom">Inclusive datetime.</param>
    /// <param name="dateTo">Exclusive datetime.</param>
    // Further work done with some help of https://stackoverflow.com/a/72742088 and https://stackoverflow.com/a/12496560 and few others sources.
    public static IQueryable<T> WhereDateTimeInRange<T>(this IQueryable<T> source, Expression<Func<T, DateTime?>> selector, DateTime? dateFrom, DateTime? dateTo)
    {
        // If both dateFrom and dateTo are null, no range is defined, so return the original source.
        if (!dateFrom.HasValue && !dateTo.HasValue)
        {
            return source;
        }

        // Get the base/starting the parameter from the selector expression.
        var parameter = selector.Parameters.Single();

        // Get the member expression from the selector expression, which represents the member (property) to be compared.
        var selectorBodyMemberExpressions = GetMemberExpressionOfNullableType(selector);

        // If the member expression is null, throw an exception.
        if (selectorBodyMemberExpressions == null)
        {
            // Not sure if this will ever happen.
            throw new BonusException("Couldn't find member expression");
        }

        // Get the key (property) to be compared using the member expression.
        var key = Expression.Property(selectorBodyMemberExpressions.Expression!, selectorBodyMemberExpressions.Member.Name);

        // using lamda function to transform value to parameterized expression
        Expression<Func<DateTime?>> expressionDateFrom = () => dateFrom;
        Expression<Func<DateTime?>> expressionDateTo = () => dateTo;

        // Check if only dateFrom has a value.
        if (dateFrom.HasValue && !dateTo.HasValue)
        {
            // Create expression for "greater than or equal" comparison.
            var from = GreaterThanOrEqual(key, expressionDateFrom.Body);

            // Apply the predicate and return the filtered source.
            return source.Where(Expression.Lambda<Func<T, bool>>(from, parameter));
        }

        // Check if only dateTo has a value.
        if (!dateFrom.HasValue && dateTo.HasValue)
        {
            // Only dateTo has a value, so create expression for "less than" comparison.
            var to = LessThan(key, expressionDateTo.Body);

            // Apply the predicate and return the filtered source.
            return source.Where(Expression.Lambda<Func<T, bool>>(to, parameter));
        }

        // Create expressions for "greater than or equal" and "less than" comparisons.
        var expressionFrom = GreaterThanOrEqual(key, expressionDateFrom.Body);
        var expressionTo = LessThan(key, expressionDateTo.Body);

        // Combine the expressions using "AND" operator.
        var predicate = Expression.Lambda<Func<T, bool>>(Expression.And(expressionFrom, expressionTo), parameter);

        // Apply the predicate and return the filtered source.
        return source.Where(predicate);
    }

    /// <summary>
    /// Extract a member expression from the provided field expression
    /// </summary>
    private static MemberExpression? GetMemberExpressionOfNullableType<TSource, TField>(Expression<Func<TSource, TField>> field)
    {
        if (field.Body is MemberExpression expressionBody)
        {
            return expressionBody;
        }

        if (field.Body is UnaryExpression expression && expression.NodeType == ExpressionType.Convert)
        {
            return (MemberExpression)expression.Operand;
        }

        return null;
    }

    // Check if both expressions are of the same types (nullable and not null)
    private static Expression GreaterThanOrEqual(Expression e1, Expression e2)
    {
        if (IsNullableType(e1.Type) && !IsNullableType(e2.Type))
        {
            e2 = Expression.Convert(e2, e1.Type);
        }
        else if (!IsNullableType(e1.Type) && IsNullableType(e2.Type))
        {
            e1 = Expression.Convert(e1, e2.Type);
        }

        return Expression.GreaterThanOrEqual(e1, e2);
    }

    // Check if both expressions are of the same types (nullable and not null)
    private static Expression LessThan(Expression e1, Expression e2)
    {
        if (IsNullableType(e1.Type) && !IsNullableType(e2.Type))
        {
            e2 = Expression.Convert(e2, e1.Type);
        }
        else if (!IsNullableType(e1.Type) && IsNullableType(e2.Type))
        {
            e1 = Expression.Convert(e1, e2.Type);
        }

        return Expression.LessThan(e1, e2);
    }

    private static bool IsNullableType(Type t)
    {
        return t.IsGenericType && t.GetGenericTypeDefinition() == typeof(Nullable<>);
    }
}