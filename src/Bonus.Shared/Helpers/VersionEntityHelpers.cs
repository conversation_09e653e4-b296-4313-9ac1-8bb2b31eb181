using Bonus.Shared.Data.Entities.Base;

namespace Bonus.Shared.Helpers;

public static class VersionEntityHelpers
{
    public static IQueryable<T> WhereCurrent<T>(this IQueryable<T> query, DateTime date)
        where T : IVersionEntity
    {
        return query
            .Where(y => y.ValidFrom <= date)
            .Where(y => y.ValidTo == null || y.ValidTo > date);
    }

    public static IQueryable<T> WherePending<T>(this IQueryable<T> query, DateTime date)
        where T : IVersionEntity
    {
        return query
            .Where(y => y.ValidFrom > date)
            .Where(y => y.ValidTo == null);
    }

    public static IQueryable<T> WhereExpired<T>(this IQueryable<T> query, DateTime date)
        where T : IVersionEntity
    {
        return query
            .Where(y => y.ValidFrom < date)
            .Where(y => y.ValidTo < date);
    }

    public static IQueryable<T> WhereCurrentAndPending<T>(this IQueryable<T> query, DateTime date)
        where T : IVersionEntity
    {
        return query
            .Where(y => (y.ValidFrom <= date && (y.ValidTo == null || y.ValidTo > date))
                        || (y.ValidFrom > date && y.ValidTo == null));
    }
}