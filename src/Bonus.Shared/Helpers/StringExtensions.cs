namespace Bonus.Shared.Helpers;

public static class StringExtensions
{
    public static string? ToNullIfWhiteSpace(this string? s)
    {
        return string.IsNullOrWhiteSpace(s) ? null : s;
    }

    public static string SafeSubstring(this string? value, int startIndex, int length)
    {
        return new string((value ?? string.Empty).Skip(startIndex).Take(length).ToArray());
    }

    public static string TakeLastSafe(this string? value, int length)
    {
        return value.TakeLastSafe(length, ' ');
    }

    public static string TakeLastSafe(this string? value, int length, char paddingChar)
    {
        return new string(value ?? string.Empty).PadLeft(length, paddingChar)[^length..];
    }
    
    public static bool HasValue(this string? value)
    {
        return !string.IsNullOrWhiteSpace(value);
    }
}