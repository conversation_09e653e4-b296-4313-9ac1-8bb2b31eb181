using System.Text.Json;

namespace Bonus.Shared.Helpers;

public static class JsonExtensions
{
    private static readonly JsonSerializerOptions CamelCaseSerializeOptions = new() { PropertyNamingPolicy = JsonNamingPolicy.CamelCase };
    private static readonly JsonSerializerOptions DeserializeOptions = new() { PropertyNameCaseInsensitive = true };

    public static string To<PERSON>son(this object? value)
    {
        return value.ToJson(JsonPropertyNamingPolicy.CamelCase);
    }

    public static string To<PERSON>son(this object? value, JsonPropertyNamingPolicy policy)
    {
        return policy switch
        {
            JsonPropertyNamingPolicy.PascalCase => JsonSerializer.Serialize(value), // This uses the default JsonSerializer settings.
            JsonPropertyNamingPolicy.CamelCase => JsonSerializer.Serialize(value, CamelCaseSerializeOptions),
            _ => throw new Exception("Requested property naming policy isn't supported.")
        };
    }

    public static T? Deserialize<T>(this string? value)
    {
        return value.HasValue() ? JsonSerializer.Deserialize<T>(value!, DeserializeOptions) : default;
    }

    public enum JsonPropertyNamingPolicy
    {
        CamelCase,
        PascalCase
    }
}