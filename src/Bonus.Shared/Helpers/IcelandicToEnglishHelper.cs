using System.Text;

namespace Bonus.Shared.Helpers;

public static class IcelandicToEnglishHelper
{
    public static string ConvertIcelandicLettersToEnglish(this string input)
    {
        var replacements = new (string Icelandic, string English)[]
        {
            ("Á", "A"), ("á", "a"),
            ("É", "E"), ("é", "e"),
            ("Í", "I"), ("í", "i"),
            ("Ó", "O"), ("ó", "o"),
            ("Ú", "U"), ("ú", "u"),
            ("Ý", "Y"), ("ý", "y"),
            ("Þ", "Th"), ("þ", "th"),
            ("Ð", "D"), ("ð", "d"),
            ("Æ", "Ae"), ("æ", "ae"),
            ("Ö", "O"), ("ö", "o")
        };

        var stringBuilder = new StringBuilder(input);

        foreach (var replacement in replacements)
        {
            stringBuilder.Replace(replacement.Icelandic, replacement.English);
        }

        return stringBuilder.ToString();
    }

    public static bool ContainsIcelandicInvariant(this string input, string value)
    {
        var safeInput = input.ConvertIcelandicLettersToEnglish();
        var safeValue = value.ConvertIcelandicLettersToEnglish();
        
        return input.Contains(value, StringComparison.InvariantCultureIgnoreCase) || 
               input.Contains(safeValue, StringComparison.InvariantCultureIgnoreCase) ||
               safeInput.Contains(value, StringComparison.InvariantCultureIgnoreCase) ||
               safeInput.Contains(safeValue, StringComparison.InvariantCultureIgnoreCase);
    }
}