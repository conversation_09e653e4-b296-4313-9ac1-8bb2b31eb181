using System.Globalization;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Text.RegularExpressions;

namespace Bonus.Shared.Converters;

public partial class UnixEpochDateTimeConverter : JsonConverter<DateTime>
{
    private static readonly DateTime Epoch = new(1970, 1, 1, 0, 0, 0);

    public override DateTime Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        var formatted = reader.GetString()!;
        var match = EpochDateRegex().Match(formatted);

        if (match.Success is false ||
            long.TryParse(match.Groups[1].Value, NumberStyles.Integer, CultureInfo.InvariantCulture, out var unixTime) is false)
        {
            throw new JsonException();
        }

        // Check if timezone offset is provided
        if (string.IsNullOrEmpty(match.Groups[2].Value))
        {
            // No timezone offset, treat as UTC
            return Epoch.AddMilliseconds(unixTime);
        }

        // Parse timezone offset
        if (int.TryParse(match.Groups[3].Value, NumberStyles.Integer, CultureInfo.InvariantCulture, out var hours) is false ||
            int.TryParse(match.Groups[4].Value, NumberStyles.Integer, CultureInfo.InvariantCulture, out var minutes) is false)
        {
            throw new JsonException();
        }

        var sign = match.Groups[2].Value[0] == '+' ? 1 : -1;
        var utcOffset = new TimeSpan(hours * sign, minutes * sign, 0);

        return Epoch.AddMilliseconds(unixTime).Add(utcOffset);
    }

    public override void Write(Utf8JsonWriter writer, DateTime value, JsonSerializerOptions options)
    {
        // Convert DateTime to Unix timestamp in milliseconds
        var unixTime = (long)(value - Epoch).TotalMilliseconds;
        
        // Format as /Date(timestamp)/ (without timezone offset since DateTime doesn't contain timezone info)
        var formatted = $"/Date({unixTime})/";
        
        writer.WriteStringValue(formatted);
    }

    [GeneratedRegex("^/Date\\(([+-]*\\d+)(?:([+-])(\\d{2})(\\d{2}))?\\)/$", RegexOptions.CultureInvariant)]
    private static partial Regex EpochDateRegex();
}