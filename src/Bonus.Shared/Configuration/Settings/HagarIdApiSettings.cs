using Bonus.Shared.Extensions;

namespace Bonus.Shared.Configuration.Settings;

public class HagarIdApiSettings : DbConfigOptionsBase
{
    public string BaseAddress { get; set; } = string.Empty;
    
    public string StartAuthCode { get; set; } = string.Empty;
    
    public string CheckAuthCode { get; set; } = string.Empty;
    
    public string AcceptTermsCode { get; set; } = string.Empty;
    
    public string Company { get; set; } = string.Empty;
    
    public string Service { get; set; } = string.Empty;
}
