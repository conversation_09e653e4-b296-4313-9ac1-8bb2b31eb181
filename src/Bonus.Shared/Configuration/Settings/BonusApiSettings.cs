using Bonus.Shared.Extensions;

namespace Bonus.Shared.Configuration.Settings;

public class BonusApiSettings : DbConfigOptionsBase
{
    public string LsRetailBaseAddress { get; set; } = string.Empty;
    
    public string TestCardIdOverride { get; set; } = string.Empty;
    
    public string StoreNumberAlwaysInStore { get; set; } = string.Empty;
    
    public string StoreFakeCoordinateLongitude { get; set; } = string.Empty;
    
    public string StoreFakeCoordinateLatitude { get; set; } = string.Empty;
}