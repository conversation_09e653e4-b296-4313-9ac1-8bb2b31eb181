using System.Resources;
using Bonus.Resources;

namespace Bonus.Shared.Types.Common.Exceptions;

public class BonusException : Exception
{
    private static readonly ResourceManager ResourceManager = new(typeof(Translations));
    
    public BonusException(string keyOrMessage) : base(keyOrMessage)
    {
        ResponseIdentifier = Guid.NewGuid().ToString();
        SetLocalizedMessage(keyOrMessage);
    }

    public BonusException(string keyOrMessage, Exception exception) : base(keyOrMessage, exception)
    {
        ResponseIdentifier = Guid.NewGuid().ToString();
        SetLocalizedMessage(keyOrMessage);
    }
    
    public BonusException(string keyOrMessage, params object?[] args) : base(keyOrMessage)
    {
        ResponseIdentifier = Guid.NewGuid().ToString();
        Args = args;

        SetLocalizedMessage(keyOrMessage);
    }
    
    public string? ResponseIdentifier { get; }
    
    public string LocalizedMessage { get; set; } = null!;

    private object?[] Args { get; } = [];
    
    private void SetLocalizedMessage(string key)
    {
        var localizedMessage = ResourceManager.GetString(key) ?? key;
        var formattedMessage = Args?.Length > 0 ? string.Format(localizedMessage!, Args) : localizedMessage;
        
        LocalizedMessage = formattedMessage.Trim().Trim(':').Trim('.'); // Trim any trailing colons or whitespace characters
    }
}