namespace Bonus.Shared.Types.Common;

public record Result<TResponse, TError>(TResponse? Response, TError? Error, string? RawResponse)
    where TResponse : class
    where TError: class
{
    public bool Success => Response is not null;
    
    public static Result<TResponse, TError> Ok(TResponse response) => new(response, null, null);
    
    public static Result<TResponse, TError> BadRequest(TError error, string rawResponse) => new(null, error, rawResponse);
}