name: Build and Package for Windows IIS

on:
  push:
    branches:
        - main
  workflow_dispatch:

jobs:
  build-and-package:
    runs-on: windows-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: '9.0.x'
        
    - name: Restore dependencies
      run: dotnet restore src/Bonus.Api/Bonus.Api.csproj
      
    - name: Build application
      run: dotnet build src/Bonus.Api/Bonus.Api.csproj --configuration Release --no-restore
            
    - name: Publish self-contained application
      run: |
        dotnet publish src/Bonus.Api/Bonus.Api.csproj `
          --configuration Release `
          --runtime win-x64 `
          --self-contained true `
          --output ./publish `
          --verbosity normal `
    
    - name: Upload artifact to GitHub
      uses: actions/upload-artifact@v4
      with:
        name: bonus-api-windows-${{ github.sha }}
        path: ./publish
        retention-days: 1