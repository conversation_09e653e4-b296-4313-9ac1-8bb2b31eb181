name: Build and deploy .NET application to container app bonusapi-app-20250311095851
on:
  push:
    branches:
    - main

permissions:
  id-token: write
  contents: read

env:
  CONTAINER_APP_CONTAINER_NAME: bonusapi
  CONTAINER_APP_NAME: bonusapi-app-20250311095851
  CONTAINER_APP_RESOURCE_GROUP_NAME: bonus
  CONTAINER_REGISTRY_LOGIN_SERVER: moberg.azurecr.io
  DOCKER_FILE_PATH: src/Bonus.Api/Dockerfile
  PROJECT_NAME_FOR_DOCKER: bonus.api

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout to the branch
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: "Setup .NET"
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: '9.0.x'
        
    - name: "Restore"
      working-directory: src
      run: dotnet restore

    - name: "Build"
      working-directory: src
      run: dotnet build -c Release --no-restore --nologo -v q --property WarningLevel=0 /clp:ErrorsOnly

    - name: "Publish"
      working-directory: src
      run: dotnet publish --no-build -c Release -p:PublishDir=./publish

    - name: Log in to container registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.CONTAINER_REGISTRY_LOGIN_SERVER }}
        username: ${{ secrets.moberg_USERNAME_CFC5 }}
        password: ${{ secrets.moberg_PASSWORD_CFC5 }}

    - name: Build and push container image to registry
      uses: docker/build-push-action@v6
      with:
        context: src
        push: true
        tags: ${{ env.CONTAINER_REGISTRY_LOGIN_SERVER }}/${{ env.PROJECT_NAME_FOR_DOCKER }}:${{ github.sha }}
        file: ${{ env.DOCKER_FILE_PATH }}

  deploy:
    runs-on: ubuntu-latest
    needs: build
    steps:
    - name: Azure Login
      uses: azure/login@v2
      with:
        client-id: ${{ secrets.AZURE_CLIENT_ID }}
        tenant-id: ${{ secrets.AZURE_TENANT_ID }}
        subscription-id: ${{ secrets.AZURE_SUBSCRIPTION_ID }}

    - name: Deploy to containerapp
      uses: azure/CLI@v2
      with:
        inlineScript: >
          az config set extension.use_dynamic_install=yes_without_prompt

          az containerapp registry set --name ${{ env.CONTAINER_APP_NAME }} --resource-group ${{ env.CONTAINER_APP_RESOURCE_GROUP_NAME }} --server ${{ env.CONTAINER_REGISTRY_LOGIN_SERVER }} --username ${{ secrets.moberg_USERNAME_CFC5 }} --password ${{ secrets.moberg_PASSWORD_CFC5 }}

          az containerapp update --name ${{ env.CONTAINER_APP_NAME }} --container-name ${{ env.CONTAINER_APP_CONTAINER_NAME }} --resource-group ${{ env.CONTAINER_APP_RESOURCE_GROUP_NAME }} --image ${{ env.CONTAINER_REGISTRY_LOGIN_SERVER }}/${{ env.PROJECT_NAME_FOR_DOCKER }}:${{ github.sha }}
    
    - name: logout
      run: >
        az logout
